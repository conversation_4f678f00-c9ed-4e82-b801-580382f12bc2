# -*- coding: utf-8 -*-
{
    'name': "Lindo - Modern UI Enhancement",

    'summary': "Modern navigation bar and sidebar UI enhancement for Odoo with responsive design",

    'description': """
Lindo - Modern UI Enhancement for Odoo
======================================

Transform your Odoo interface with a modern, responsive design featuring:

🎨 **Modern Design Elements**
* Beautiful gradient navigation bar with smooth animations
* Collapsible sidebar with intuitive icon-based navigation
* Card-based layout with hover effects and shadows
* Enhanced form and table styling

📱 **Responsive & Mobile-Friendly**
* Adaptive layout for different screen sizes
* Mobile-optimized navigation with hamburger menu
* Touch-friendly interface elements

⚡ **Enhanced User Experience**
* Smooth CSS transitions and animations
* Interactive hover effects and click feedback
* Persistent sidebar state (remembers collapsed/expanded preference)
* Improved visual hierarchy and readability

🔧 **Technical Features**
* Modular CSS and JavaScript architecture
* Compatible with Odoo's web framework
* Cross-browser support for modern browsers
* Performance optimized with lightweight assets
* Easy customization and theming options

**Installation & Usage**
1. Install the module from Apps menu
2. The enhanced UI will be automatically applied
3. Access demo pages via the Lindo menu
4. Customize colors and layout in the CSS files

**Demo Available**
Visit /lindo/static/demo.html for a standalone demonstration of all UI components.

Perfect for businesses looking to modernize their Odoo interface with minimal setup and maximum visual impact.
    """,

    'author': "Lindo Development Team",
    'website': "https://github.com/lindo-ui/odoo-modern-ui",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Website/Theme',
    'version': '********.2',
    'license': 'LGPL-3',
    'installable': True,
    'auto_install': False,
    'application': False,

    # any module necessary for this one to work correctly
    'depends': ['base', 'web'],

    # always loaded
    'data': [
        'security/lindo_security.xml',
        # 'security/ir.model.access.csv',
        'views/views.xml',
        'views/templates.xml',
        # 'views/hr_employee_custom_views.xml',
    ],

    # Assets for web interface
    'assets': {
        'web.assets_backend': [
            'lindo/static/src/css/lindo_ui.css',
            'lindo/static/src/js/lindo_ui.js',
            'lindo/static/src/scss/hr_custom_design.scss',
            'lindo/static/src/scss/hr_employee_custom.scss',
            'lindo/static/src/js/hr_employee_custom.js',
            
            
            
        ],
        'web.assets_frontend': [
            'lindo/static/src/css/lindo_ui.css',
            'lindo/static/src/js/lindo_ui.js',
           
        ],
    },
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
}

