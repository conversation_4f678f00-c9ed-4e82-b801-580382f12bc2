<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="ks_access_manager_ninja.reset_password_direct" name="Reset password">
        <t t-call="web.login_layout">
            <form class="oe_reset_password_form" role="form" action="/web/reset_password/submit" method="post">
                <div class="form-group field-login">
                    <label for="user_name" class="col-form-label">User Name</label>
                    <input type="text" name="user_name" t-att-value="username" id="user_name"
                           class="form-control"
                           autofocus="autofocus" required="required" autocapitalize="off"/>
                </div>
                <div class="form-group field-login">
                    <label for="old_password" class="col-form-label">Old Password</label>
                    <input type="password" name="old_password" t-att-value="old_password" id="old_password"
                           class="form-control"
                           autofocus="autofocus" required="required" autocapitalize="off"/>
                </div>
                <label for="new_password" class="col-form-label">New Password</label>

                <div class="form-group field-login" style="display:flex;">
                    <input type="password" name="new_password" t-att-value="new_password" id="new_password"
                           class="form-control"
                           autofocus="autofocus" required="required" autocapitalize="off"/>
                    <span class="fa fa-eye o_little_custom_eye input-group-text" aria-hidden="true"
                          style="cursor: pointer;"></span>
                </div>
                <div class="form-group field-login">
                    <label for="confirm_new_password" class="col-form-label">Confirm New Password</label>
                    <input type="password" name="confirm_new_password" t-att-value="confirm_new_password"
                           id="confirm_new_password" class="form-control"
                           autofocus="autofocus" required="required" autocapitalize="off"/>
                </div>
                <p class="alert alert-danger" t-if="error" role="alert">
                    <t t-esc="error"/>
                </p>
                <div class="clearfix oe_login_buttons mt-3">
                    <button type="submit" class="btn btn-primary btn-block">Confirm</button>
                    <div class="d-flex justify-content-between align-items-center small mt-2">
                        <a t-attf-href="/web/login">Back to Login</a>
                    </div>
                </div>
            </form>
        </t>
    </template>
    <template id="ks_access_manager_ninja.reset_password_link" inherit_id="auth_signup.login" name="Reset Password">
        <xpath expr="//button[@type='submit']" position="after">
            <div class="justify-content-between mt-2 d-flex small">
                <a t-if="reset_password_enabled" t-attf-href="/web/reset_password/direct">Change Password
                </a>
            </div>
        </xpath>
    </template>
</odoo>