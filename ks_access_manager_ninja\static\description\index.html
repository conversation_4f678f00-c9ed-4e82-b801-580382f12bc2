<!DOCTYPE html>
<html lang="en">

<head>
    <title>Access Ninja | Descrition</title>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

</head>

<body>
    <section class="py-3" style="background-color: #F7FFD6;">
        <div class="container">
            <div class="mb-4 bg-white shadow " style="border-radius: 20px; font-family: 'Inter', sans-serif;">
                <div class="px-md-5 py-md-4 p-2">
                    <div class="ks-top-logos pb-lg-5 pb-4">
                        <div class="text-center mb-md-4 mb-3">
                            <img src="./images/access-ninja-img/top-logo-1.png" class="img-fluid" alt="ks-logo" height="60" loading="lazy">
                        </div>
                    </div>
                    <div class="ks-top-buttons pb-lg-5 pb-4">
                        <div class="row">
                            <div class="col-lg-5 col-sm-12"></div>
                            <div class="col-lg-7 col-12">
                                <div class="d-flex align-items-center justify-content-between flex-sm-row flex-column">
                                    <div>
                                        <a href="#all_suggested-app" class="d-flex align-items-center justify-content-center px-2 mb-sm-0 mb-3" style="text-decoration: none; height:48px; background-color: #013066; color:#fff;border-radius: 5px;">
<!--                                            <img src="./images/access-ninja-img/cale.svg" alt="icon" height="20" class="mr-2" />-->
                                            <i class="fa fa-list-alt mr-2"></i>
                                            <span class="font-weight: 500; font-size: 18px; line-height: 20px; text-transform: capitalize;">Ksolves All Apps</span>
                                        </a>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="d-flex align-items-center justify-content-center px-2 mr-3" style="height:48px; background-color: #013066; color:#fff;border-radius: 5px;">
<!--                                            <img src="./images/access-ninja-img/tick.svg" alt="icon" height="14" class="mr-2" />-->
                                            <i class="fa fa-check mr-2"></i>
                                            <span class="font-weight: 500; font-size: 18px; line-height: 20px; text-transform: capitalize;">Community</span>
                                        </div>
                                        <div class="d-flex align-items-center justify-content-center px-2" style="height:48px; background-color: #013066; color:#fff;border-radius: 5px;">
<!--                                            <img src="./images/access-ninja-img/tick.svg" alt="icon" height="14" class="mr-2" />-->
                                            <i class="fa fa-check mr-2"></i>
                                            <span class="font-weight: 500; font-size: 18px; line-height: 20px; text-transform: capitalize;">Enterprise</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ks-main-text">
                        <h1 style="font-weight: 700; font-size: 48px; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(2.3rem + 0.86vw);">
                            Access Manager Ninja
                        </h1>
                        <p style="font-weight: 500; font-size: 18px; line-height: 27px; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;" class="py-lg-4 py-3">
                            Streamline your workflow and save valuable time and effort by implementing Instant Access, a cutting-edge management application. This centralized platform allows you to grant specific rights to profiles, ensuring their access to fields, models, menus, records, buttons, tabs, actions, and other essential components.
                        </p>
                        <p style="font-weight: 500; font-size: 18px; line-height: 27px; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                            With a strong emphasis on honesty and defined security, you can tailor user permissions based on their individual needs and working conditions. By tackling the intricacies of your work and enabling efficient collaboration, instant Access simplifies your tasks and boosts productivity.
                        </p>
                    </div>
                </div>
                <div class="pt-4 px-md-4 px-3">
                    <ul class="nav nav-tabs justify-content-center bg-white pt-md-2" id="myTab" role="tablist"
                        style="border-bottom:1px solid #CFCFCF;">
                        <li class="nav-item">
                            <a aria-controls="overview" aria-selected="true" class="nav-link active" data-toggle="tab"
                                href="#overview" id="overview-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">
                                Overview</a>
                        </li>
                        <li class="nav-item">
                            <a aria-controls="setup" aria-selected="false" class="nav-link" data-toggle="tab"
                                href="#setup" id="setup-tab" role="tab" style="color: #4F4F4F;font-weight: 400; font-size: 14px;">Setup</a>
                        </li>
                        <li class="nav-item">
                            <a aria-controls="releases" aria-selected="false" class="nav-link" data-toggle="tab"
                                href="#releases" id="releases-tab" role="tab"
                                style="color: #4F4F4F;font-weight: 400; font-size: 14px;">Release</a>
                        </li>
                    </ul>
                </div>
                <div class="tab-content p-md-5 p-2 py-3" id="myTabContent">

                    <div aria-labelledby="overview-tab" class="tab-pane fade show active" id="overview" role="tabpanel">
                        <!-- 1 -->
                        <div class="position-relative mb-4" style="border-radius: 8px;">
                            <img src="./images/access-ninja-img/bg-img/bg1.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" />
                            <div class="px-lg-4 py-lg-5 p-3 position-relative">
                                <div class="row py-4 py-lg-0">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 700; word-wrap: break-word; font-size: 48px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2.06rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(2.06rem + 1vw);">Profile Management</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Create and design user profiles accessible to multiple users, providing the advantage of temporarily activating or blocking profiles.
                                        </p>
                                    </div>
                                    <div class="col-12">
                                        <div class="row">
                                            <div class="col-lg-6 col-12 mb-3 ">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Read-Only Rights and Hide Chatter
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Admin have the power to assign user a read-only access and ability to hide the chatter from designated profiles.
                                                        </p>
                                                        <br class="d-lg-block d-none">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Disable Developer Mode
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Utilize configuration techniques to achieve a seamless experience, disabling developers from specified profiles.
                                                        </p>
                                                        <br class="d-lg-block d-none">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3 ">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Hide Menu Rights
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Tailor access rights for specific user groups and dynamically hide menus and sub-menus based on relevant data, ensuring convenience and efficiency.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Give Model-Access Permissions
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Effortlessly manage and control access to groups, models, and essential components such as reports and actions from a centralized platform.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3 ">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Dynamic Permissions
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Customize user profiles to match their authenticity, assigning rights for CREATE, UPDATE, DELETE, and READ.
                                                        </p>
                                                        <br class="d-lg-block d-none">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Hide Filters and Group by
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Admin have the right to hide filters and group by permissions from specific profile.
                                                        </p>
                                                        <br class="d-lg-block d-none">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3 ">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Hide Buttons / Tabs
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Admin can hide any Button/Tab from any model for any profile.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Company Wise Restrictions
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            This functionality is applicable across multiple companies, allowing you to specify the companies for which the rule should be applied.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3 ">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Field Access
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Customize field settings for different users, including options to make fields INVISIBLE, REQUIRED, READONLY, or REMOVE EXTERNAL LINK.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-12 mb-3">
                                                <div class="bg-white h-100" style="padding: 16px; border-radius: 32px;">
                                                    <div class="p-3 h-100" style="border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 32px;">
                                                        <h6 style="font-weight: 700; font-size: 24px; line-height: 120%; letter-spacing: -0.02em; text-transform: capitalize; color: #203038;">
                                                            Hide Duplicate / Archive / Export
                                                        </h6>
                                                        <p style="font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                                            Admin can hide some actions such as ARCHIVE, DUPLICATE, and EXPORT.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mt-3">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 700; word-wrap: break-word; font-size: 48px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2.06rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(2.06rem + 1vw);">temporary Activation
                                                <br class="d-lg-block d-none">
                                                and blocking Profiles
                                            </span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Get an instant option to manage multiple profiles by single user. You can assign profiles with different groups and permit them some rights. Along with that, take an advantage of temporarily activate and block the profiles as per needs.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 700; word-wrap: break-word; font-size: 48px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2.06rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(2.06rem + 1vw);">Password Expiration
                                            </span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Admin can work on making the password more secure and set the expiration date for users in just one click.
                                        </p>
                                    </div>
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 700; word-wrap: break-word; font-size: 48px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(2.06rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(2.06rem + 1vw);">Admin Login Access
                                            </span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%;  text-align: center; letter-spacing: -0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Admin have the access to login into any user account and even make them logout at any time. Along with this User's activity time gets recorded that saves the login and logot time of the users.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 2 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg2.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">MANAGE MULTIPLE PROFILES</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Get an instant option to manage multiple profiles by single user. You can assign profiles with different groups and permit them some rights. Along with that, take an advantage of temporarily activate and block the profiles as per needs.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical"
                                                    class="nav flex-row h-100 bg-transparent nav-pills mb-4"
                                                    id="v-pills-tab" role="tablist" style="border-radius:  8px;">

                                                    <a aria-controls="dashboard" class="nav-link w-100 p-0 border-bottom active" data-toggle="tab" href="#dashboard" role="tab" style="border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; text-align: center; text-transform: capitalize; color: #013066;">
                                                                    Profile
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 h-100 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane  h-100  active" id="dashboard" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div >
                                                                <img  src="./images/access-ninja-img/1/a1.gif" alt="gif" class="img-fluid" loading="lazy" style="border-radius: 8px; ">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 3 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg3.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">GIVE ADDITIONAL PERMISSIONS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Admin have different customization options: he can disable developer mode, gives the user a readonly option, and hide chatter for any user/profile.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="readonly" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#readonly" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Model Readonly
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="chatter" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#chatter" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Chatter
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="developers" class="nav-link w-100 p-0 border-bottom"
                                                        data-toggle="tab" href="#developers" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Disables Developers
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 h-100 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100  active" id="readonly" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;"
                                                                    src="./images/access-ninja-img/2/readonly.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="chatter" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;"
                                                                    src="./images/access-ninja-img/2/chatter.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="developers" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;"
                                                                    src="./images/access-ninja-img/2/developers.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 4 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg4.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">HIDE MENUS/SUB-MENUS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                           Configure user-based access rights to hide navigation menus and submenus for specific profiles.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical"
                                                    class="nav flex-row h-100 bg-transparent nav-pills mb-4"
                                                    id="v-pills-tab" role="tablist" style="border-radius:  8px;">

                                                    <a aria-controls="hide-menu" class="nav-link w-100 p-0 border-bottom active" data-toggle="tab" href="#hide-menu" role="tab" style="border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; text-align: center; text-transform: capitalize; color: #013066;">
                                                                    Hide Menus
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 h-100 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane  h-100  active" id="hide-menu" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div >
                                                                <img  src="./images/access-ninja-img/3/hm.gif" alt="gif" class="img-fluid" loading="lazy" style="border-radius: 8px; ">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 5 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg5.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">MODEL ACCESS RIGHTS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Set up and define access permissions for specific profile in a particular model, Admin can hide some actions such as REPORT, ACTIONS, CREATE, EDIT, DELETE, ARCHIVE, DUPLICATE, EXPORT and can make any model READONLY.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="readonly1" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#readonly1" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Reports
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="chatter2" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#chatter2" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Actions
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="developers1" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#developers1" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Model Readonly
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="create" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#create" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Create
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="del" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#del" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Delete
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="edit" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#edit" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Edit
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="ha" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#ha" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Archive
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="hd" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#hd" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Duplicate
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="he" class="nav-link w-100 p-0 border-bottom"
                                                        data-toggle="tab" href="#he" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Export
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100  active" id="readonly1" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/hr.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="chatter2" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/ah.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="developers1" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/mr.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="create" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/create.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="del" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/del.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="edit" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/edit.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="ha" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/ha.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="hd" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/hd.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="he" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/4/eh.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 6 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg6.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">FIELD ACCESS RIGHTS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Establish a connection to manage access rights for various fields. Customize field settings for different users, including options to make fields INVISIBLE, REQUIRED, READONLY, or REMOVE EXTERNAL LINK.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="f1" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#f1" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Field Invisible
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="f2" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#f2" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Field Readonly
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="f3" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#f3" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Remove External Link
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="f4" class="nav-link w-100 p-0 border-bottom"
                                                        data-toggle="tab" href="#f4" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Field Required
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>


                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100  active" id="f1" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/5/f1.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="f2" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/5/f2.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="f3" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/5/f3.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="f4" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/5/f4.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 7 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg7.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">DOMAIN ACCESS PERMISSIONS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Admin have the rights to make some records restrict conditionally for few users. Based on the field and value, he can give access to CREATE, EDIT, DELETE, and READ.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="a1" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#a1" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Domain Access
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100  active" id="a1" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/6/da.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 8 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg8.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">BUTTON/TAB ACCESS RIGHTS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Control the permissions to hide any Buttons/Kanban links/Tabs from specific profiles in different models.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="d1" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#d1" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Button
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="d13" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#d13" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Kanban Links
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="d2" class="nav-link w-100 p-0 border-bottom"
                                                        data-toggle="tab" href="#d2" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Tab
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100  active" id="d1" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/7/btn.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100  active" id="d13" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/7/kbtn.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="d2" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/7/tbtn.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 9 -->
                        <div class="mb-4 position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg9.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">HIDE FILTERS AND GROUP BY</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Admin have the right to hide filters and group by permissions from specific profiles.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="d11" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#d11" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Filter
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="d22" class="nav-link w-100 p-0 border-bottom"
                                                        data-toggle="tab" href="#d22" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Hide Group by
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100  active" id="d11" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/8/sf.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="d22" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/8/grp.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 10 -->
                        <div class="position-relative" style="border-radius: 10px;">
                            <img src="./images/access-ninja-img/bg-img/bg10.png" style="left: 0;" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" >
                            <div class="p-md-5 p-3 position-relative">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <h1 style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 36px; line-height: 120%; text-align: center; letter-spacing: -0.02em; text-transform: capitalize; color: #203038; font-size: calc(1.44rem + 1vw); ">
                                            <span style="color: #203038; font-size: calc(1.44rem + 1vw);">PASSWORD AND ACTIVITY PERMISSIONS</span>
                                        </h1>
                                    </div>
                                    <div class="col-md-10 offset-md-1 py-md-3 py-2">
                                        <p style="font-family: Inter; font-style: normal; font-weight: 500; font-size: 18px;
                                        line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            Admin have the ability to set the expiration date for all the users and the mail will be sent automatically before 7 days and 1 day. There has been proper visibility of login and logout activity of users and anytime Admin can login and logout into any user account.
                                            Excel sheet of Groups will be saved on attachments after installing Access Manager Ninja.
                                        </p>
                                    </div>
                                    <div class="col-md-12 mt-2">
                                        <div class="row no-gutters">
                                            <div class="col-md-3">
                                                <div aria-orientation="vertical" class="nav flex-column justify-content-start h-100 bg-transparent nav-pills mb-4" id="v-pills-tab" role="tablist" style="border-radius: 8px;">

                                                    <a aria-controls="pe" class="nav-link w-100 p-0 border-bottom mb-3 active"
                                                        data-toggle="tab" href="#pe" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Password Expiration
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="la" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#la" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Login access
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                    <a aria-controls="ra" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#ra" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Recent Activities
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a aria-controls="eg" class="nav-link w-100 p-0 border-bottom mb-3"
                                                        data-toggle="tab" href="#eg" role="tab" style=" border-radius: 8px; padding-right:4px !important;height:45px">
                                                        <div class="media bg-white align-items-center px-2 py-3" style="height:45px; border-radius: 8px;">
                                                            <div class="media-body pl-2 pr-2 position-relative">
                                                                <h5 class="d-flex  align-items-center m-0 text-left"
                                                                    style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 600; font-size: 16px; line-height: 150%; /* identical to box height, or 24px */ text-align: center; text-transform: capitalize; /* Gray 2 */ color: #013066;">
                                                                    Groups Export
                                                                </h5>
                                                            </div>
                                                        </div>
                                                    </a>

                                                </div>
                                            </div>
                                            <div class="col-md-9 ">
                                                <div class="tab-content p-4 bg-white ml-md-4 ml-0 shadow-sm"
                                                    style="border-radius:8px">

                                                    <div class="tab-pane h-100 active" id="pe" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="gif" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/9/last.gif">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="la" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="img" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/9/la.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="ra" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="img" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/9/ra.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane h-100" id="eg" role="tabpanel">
                                                        <div class="h-100 d-flex align-items-center w-100 justify-content-center ">
                                                            <div>
                                                                <img alt="img" class="img-fluid" loading="lazy" style="border-radius:8px;" src="./images/access-ninja-img/9/eg.gif">
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div aria-labelledby="setup-tab" class="tab-pane fade show" id="setup" role="tabpanel">

                        <!-- 1 -->
                        <div class="position-relative mb-4" style="border-radius: 8px;">
                            <img src="./images/access-ninja-img/bg-img/s1.png" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" />
                            <div class="px-lg-5 p-3 position-relative">
                                <div class="row py-4 py-lg-0">
                                    <div class="col-12">
                                        <h1 class="px-xl-4 mt-lg-4 mt-3" style="font-family: 'Inter', sans-serif; font-style: normal; font-weight: 700; word-wrap: break-word; font-size: 32px; line-height: 120%;text-align: center; text-transform: capitalize; font-size: calc(1.5rem + 0.4vw); ">
                                            <span style="color: #203038; font-size: calc(1.5rem + 0.4vw);">After the installation, only Admin can access: User Profile and Profile Management.
                                            </span>
                                        </h1>
                                    </div>
                                    <div class="col-12 mt-3">
                                        <p style="font-weight: 500; font-size: 24px; line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #203038;" class="mb-0">
                                            From the Menu >> Settings >> Users & Companies >> User Profile.
                                        </p>
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            1. create a user profile.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <img src="./images/access-ninja-img/w1.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            2. Select the user to whom you want to apply this profile
                                        </p>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <img src="./images/access-ninja-img/w2.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            3. Select the multiple profiles inside user's as per requirement.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <img src="./images/access-ninja-img/w9.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            4. Select the group to which you want to apply for the selected users.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-4">
                                        <img src="./images/access-ninja-img/w3.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 mb-sm-3">
                                        <img src="./images/access-ninja-img/w4.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>


                                </div>
                            </div>
                        </div>
                        <!-- 2 -->
                        <div class="position-relative mb-4" style="border-radius: 8px;">
                            <img src="./images/access-ninja-img/bg-img/s2.png" alt="" loading="lazy" class="w-100 h-100 position-absolute img-fluid" />
                            <div class="px-lg-5 p-3 position-relative">
                                <div class="row py-4 py-lg-0">
                                    <div class="col-12 mt-3">
                                        <p style="font-weight: 500; font-size: 24px; line-height: 150%; text-align: center; letter-spacing: 0.02em; text-indent: 2px; color: #203038;" class="mb-0">
                                            Go to Menu >> Settings >> Users & Companies >> Profile Management.
                                        </p>
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            5. Create a profile management system to modify the access rights of allowed groups or restrict rights for fields, models, and some other actions.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <img src="./images/access-ninja-img/w5.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            6. Choose the profile and companies for which you want to restrict certain rights.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <img src="./images/access-ninja-img/w6.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            7. The created management system will be implemented for users within the profile.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-4">
                                        <img src="./images/access-ninja-img/w7.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>
                                    <div class="col-12 py-md-3 py-2">
                                        <p style="font-family: Inter; text-align:left; font-style: normal; font-weight: 500; font-size: 18px; line-height: 150%; letter-spacing: 0.02em; text-indent: 2px; color: #4F4F4F;">
                                            8. As a result, the selected menu will be hidden for the specified profile and for users within that specific smart button.
                                        </p>
                                    </div>
                                    <div class="col-12 mb-sm-3">
                                        <img src="./images/access-ninja-img/w8.png" alt="img" class="img-fluid" style="border-radius: 8px;">
                                    </div>


                                </div>
                            </div>
                        </div>

                    </div>

                    <div aria-labelledby="releases-tab" class="tab-pane fade show" id="releases" role="tabpanel">
                        <div class="py-2" style="margin-top: -38px; margin-bottom: -32px;">
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4 style="font-size:16px; color:#000000; margin:0; line-height:26px">
                                        Latest Release 1.1.0
                                    </h4>
                                    <span style="font-size:14px; color:#5E5E5E; display:block; margin-bottom:20px">
                                        26th October 2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div style="display:flex; width:fit-content; padding:0px 8px; color: #000000; background-color: #E9FF90; border-radius:20px">
                                                New Feature
                                            </div>
                                        </div>
                                        <ul style="color:#5E5E5E; font-family:'Inter', sans-serif" class="pl-3">
                                            <li>
                                                <span style="color:#000000; display:flex; font-size: 18px;">Added privilege to hide Kanban Links and Kanban Buttons.</span>
                                            </li>

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4 style="font-size:16px; color:#000000; margin:0; line-height:26px">
                                        Release 1.0.1
                                    </h4>
                                    <span style="font-size:14px; color:#5E5E5E; display:block; margin-bottom:20px">
                                        21st September 2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div style="display:flex; width:fit-content; padding:0px 8px; color: #000000; background-color: #E9FF90; border-radius:20px">
                                                Fixes
                                            </div>
                                        </div>
                                        <ul style="color:#5E5E5E; font-family:'Inter', sans-serif" class="pl-3">
                                            <li>
                                                <span style="color:#000000; display:flex; font-size: 18px;">Show models in the dropdown based on the selected profiles.</span>
                                            </li>
                                            <li>
                                                <span style="color:#000000; display:flex; font-size: 18px;">Fixed smart button dropdown and Hide the smart button based on profile.</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="row pt-5 m-0">
                                <div class="col-md-3">
                                    <h4 style="font-size:16px; color:#000000; margin:0; line-height:26px">
                                        Release 1.0.0
                                    </h4>
                                    <span style="font-size:14px; color:#5E5E5E; display:block; margin-bottom:20px">
                                        16th July 2023
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <!-- Bug Fixed -->
                                    <div style="padding:0 0 40px">
                                        <div style="margin:0 0 10px">
                                            <div style="display:flex; width:fit-content; padding:0px 8px; color: #000000; background-color: #E9FF90; border-radius:20px">
                                                New Version
                                            </div>
                                        </div>
                                        <ul style="color:#5E5E5E; font-family:'Inter', sans-serif" class="pl-3">
                                            <li>
                                                <span style="color:#000000; display:flex; font-size: 18px;">Release of Access Manager Ninja</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>

            <section>

                <div class="shadow pt-lg-5 pb-4 bg-white" id="free_support" style="margin-top: 42px; border-radius: 20px;">
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-11 px-0 mx-auto pb-lg-5 position-relative overflow-hidden" style="border-radius: 20px; background-color: #fffefd;">
                            <img src="./img/support/support-bg.png" alt="bg-img" class="h-100 w-100 position-absolute fix-bottom img-fluid"  loading="lazy" style="object-fit: cover;top: 0;left: 0;">
                            <div class="row position-relative pt-lg-5 px-lg-5 pb-lg-3 p-md-4 p-3 mx-auto d-flex align-items-center" style="color: #333333;font-weight: 500;font-size: 16px;width: 95%;">
                                <div class="col-lg-auto col-sm-12">
                                    <img src="./img/support/support.png" width="219"/>
                                </div>
                                <div class="col pl-lg-5 pl-0 pt-lg-0 pt-3">
                                    <h5 class="text-left" style=" font-family: 'Inter', sans-serif;margin-bottom: 15px;font-style: normal;font-weight: 600;font-size: 36px;line-height: 44px;letter-spacing: 0.02em;text-transform: uppercase;color: #FFFFFF;">
                                        Free <span style="color:#9EFFFF; font-weight: 800; font-family: 'Inter', sans-serif;">90 Days</span> Support
                                    </h5>
                                    <p style="font-style: normal;font-weight: 500;font-size: 18px;line-height: 28px;text-transform: capitalize;color: #FFFFFF;margin: 0; font-family: 'Inter', sans-serif;">Ksolves will provide FREE 90 days support for any doubt, queries, and bug fixing (excluding data recovery) or any type of issues related to this module. This is applicable from the date of purchase.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row d-flex align-items-center justify-content-center m-0">
                        <div class="col-12 p-0 pt-4 overflow-hidden text-center">
                            <p style="font-style: normal;font-weight: 500;font-size: 18px;line-height: 28px;letter-spacing: 0.01em;color: #535456;margin: 0;"><span style="font-style: normal;font-weight: 600;font-size: 24px;line-height: 28px;letter-spacing: 0.01em;color: #283C63;margin: 0 10px 0 0;">Note</span> Extensively Tested on Odoo Vanilla with Ubuntu OS</p>
                        </div>
                    </div>
                </div>

                <div class="shadow bg-white" id="all_suggested-app" style="margin-top: 42px; border-radius: 20px; padding-top: 41px; padding-bottom: 30px;">
                    <h5 class="text-center" style="color: #2B5FB2; font-weight: 700; font-size: 32px; line-height:43px; margin-bottom: 25px; font-size: calc(1.5rem + 0.4vw); font-family: 'Inter', sans-serif;">
                        <span style="color:#333333; font-family: 'Inter', sans-serif;">Ksolves Suggested Apps</span>
                    </h5>
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-11 pt-lg-3 px-lg-5 pb-lg-2 p-md-4 p-3 position-relative overflow-hidden" style="border-radius: 10px; background-color: #fffefd;">
                            <div class="row position-relative" style="color: #333333; font-weight: 500; font-size: 16px; ">

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_dashboard_ninja/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/Dashboard-Ninja.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Dashboard Ninja
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_dn_advance/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/Dashboard-Ninja-Advance.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Dashboard Ninja Advance
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_woocommerce/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/Odoo-WooCommerce.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Odoo WooCommerce Connector
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/themes/14.0/ks_curved_backend_theme/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/arc-backend-theme.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Arc Backend Theme
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_gantt_view_base/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/odoo-base.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Odoo Gantt View Base
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_list_view_manager/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/list-view-manager.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    List View Manager
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_custom_report/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/report.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    ReportMate
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_dynamic_financial_report/" style="text-decoration:none" target="_blanck">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC; border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/dfr.png" alt="icon" style="border-radius: 5px;" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Dynamic Financial Report
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <div class="col-sm-6 col-lg-4 col-xl-4 mb-3 pr-lg-0 pr-sm-1">
                                    <a href="https://apps.odoo.com/apps/modules/15.0/ks_shopify/" target="_blanck" style="text-decoration:none">
                                        <div class="d-flex  align-items-center h-100 bg-white px-3 py-3" style="border: 1px solid #EFECEC;border-radius: 8px;">
                                            <div style="width:100px;">
                                                <img src="./img/support/shopify.png" alt="icon" loading="lazy" width="100" height="100">
                                            </div>
                                            <div class="w-100 pl-3">
                                                <p style="font-style: normal;font-weight: 500;font-size: 15px;line-height: 150%;text-indent: 2px;color: #535456;" class="mb-0">
                                                    Odoo Shopify Connector
                                                </p>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>

                <div class="shadow bg-white p-0" id="free_support" style="margin-top: 42px; border-radius: 20px; padding-top: 41px; padding-bottom: 41px;">
                    <div class="row d-flex align-items-center justify-content-center m-0">
                        <div class="col-12 px-0 position-relative overflow-hidden" style="border-radius: 20px; background-color: #fffefd;">
                            <img src="./img/support/ks-odoo-services-bg.png" alt="bg-img" class="h-100 w-100 position-absolute fix-bottom img-fluid"  loading="lazy" style="object-fit: cover;top: 0;left: 0;">
                            <div class="row position-relative pt-lg-3 px-lg-5 pb-lg-3 p-md-4 p-3 mx-auto d-flex align-items-center" style="color: #333333;font-weight: 500;font-size: 16px;width: 100%;gap: 35px 70px;">
                                <div class="col text-center py-4">
                                    <h5 class="text-center mb-4" style="font-style: normal;font-weight: 700;font-size: 32px;line-height: 48px;letter-spacing: -0.02em;text-indent: 2px;color: #9EFFFF; font-size: calc(1.5rem + 0.4vw); font-family: 'Inter', sans-serif;">
                                        Ksolves Odoo Services
                                    </h5>
                                    <img src="./img/support/services-img.png" width="685" class="img-fluid"/>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </section>
        </div>
    </section>

    <script src="js/jquery.slim.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
</body>

</html>
