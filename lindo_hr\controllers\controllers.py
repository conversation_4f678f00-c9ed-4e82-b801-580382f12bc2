# -*- coding: utf-8 -*-
# from odoo import http


# class LindoHr(http.Controller):
#     @http.route('/lindo_hr/lindo_hr', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/lindo_hr/lindo_hr/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('lindo_hr.listing', {
#             'root': '/lindo_hr/lindo_hr',
#             'objects': http.request.env['lindo_hr.lindo_hr'].search([]),
#         })

#     @http.route('/lindo_hr/lindo_hr/objects/<model("lindo_hr.lindo_hr"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('lindo_hr.object', {
#             'object': obj
#         })

