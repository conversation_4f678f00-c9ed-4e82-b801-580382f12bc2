<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- واجهة نموذج الموظف المحسنة -->
        <record id="view_employee_form_custom" model="ir.ui.view">
            <field name="name">hr.employee.form.custom</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="attributes">
                    <attribute name="class">hr_custom_employee_form</attribute>
                </xpath>
                
                <!-- تحسين منطقة الصورة والعنوان -->
                <xpath expr="//div[hasclass('row', 'justify-content-between')]" position="replace">
                    <div class="employee_header_section">
                        <div class="employee_info_card">
                            <div class="employee_avatar_section">
                                <div class="avatar_container">
                                    <field name="image_1920" widget='image' class="employee_avatar_img" options='{"zoom": true, "preview_image":"avatar_128"}'/>
                                    <field name="show_hr_icon_display" invisible="1" />
                                    <field name="hr_icon_display" class="employee_status_indicator" invisible="not show_hr_icon_display or not id" widget="hr_presence_status"/>
                                </div>
                            </div>
                            <div class="employee_details_section">
                                <div class="employee_name_section">
                                    <h1 class="employee_name">
                                        <div invisible="not user_id" class="chat_icon_container">
                                            <widget name="hr_employee_chat" invisible="not context.get('chat_icon')"/>
                                        </div>
                                        <field name="name" placeholder="اسم الموظف" required="True"/>
                                    </h1>
                                    <h2 class="employee_job_title">
                                        <field name="job_title" placeholder="المنصب الوظيفي" />
                                    </h2>
                                    <div class="employee_tags">
                                        <field name="category_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}" placeholder="العلامات" groups="hr.group_hr_user"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>

                <!-- تحسين قسم المعلومات الأساسية -->
                <xpath expr="//group[1]" position="replace">
                    <div class="employee_basic_info_section">
                        <div class="info_card contact_info">
                            <h3 class="card_title">
                                <i class="fa fa-phone"/> معلومات الاتصال
                            </h3>
                            <div class="info_grid">
                                <div class="info_item">
                                    <label class="info_label">الهاتف المحمول</label>
                                    <field name="mobile_phone" widget="phone" class="info_field"/>
                                </div>
                                <div class="info_item">
                                    <label class="info_label">هاتف العمل</label>
                                    <field name="work_phone" widget="phone" class="info_field"/>
                                </div>
                                <div class="info_item">
                                    <label class="info_label">البريد الإلكتروني</label>
                                    <field name="work_email" widget="email" class="info_field"/>
                                </div>
                                <div class="info_item" groups="base.group_multi_company">
                                    <label class="info_label">الشركة</label>
                                    <field name="company_id" class="info_field"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="info_card work_info">
                            <h3 class="card_title">
                                <i class="fa fa-briefcase"/> معلومات العمل
                            </h3>
                            <div class="info_grid">
                                <div class="info_item">
                                    <label class="info_label">القسم</label>
                                    <field name="department_id" class="info_field"/>
                                </div>
                                <div class="info_item">
                                    <label class="info_label">المنصب</label>
                                    <field name="job_id" class="info_field"/>
                                </div>
                                <div class="info_item">
                                    <label class="info_label">المدير</label>
                                    <field name="parent_id" widget="many2one_avatar_user" class="info_field"/>
                                </div>
                                <div class="info_item">
                                    <label class="info_label">المدرب</label>
                                    <field name="coach_id" widget="many2one_avatar_user" class="info_field"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>

                <!-- إخفاء الحقول المخفية -->
                <field name="company_country_id" position="attributes">
                    <attribute name="invisible">1</attribute>
                </field>
                <field name="company_country_code" position="attributes">
                    <attribute name="invisible">1</attribute>
                </field>
            </field>
        </record>

        <!-- واجهة كانبان محسنة للموظفين -->
        <record id="hr_kanban_view_employees_custom" model="ir.ui.view">
            <field name="name">hr.employee.kanban.custom</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.hr_kanban_view_employees"/>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="class">o_hr_employee_kanban_custom</attribute>
                </xpath>
                
                <xpath expr="//div[hasclass('oe_kanban_global_click')]" position="replace">
                    <div class="oe_kanban_global_click employee_kanban_card">
                        <div class="employee_card_header">
                            <div class="employee_avatar_kanban">
                                <img t-att-src="kanban_image('hr.employee', 'avatar_128', record.id.raw_value)" 
                                     class="oe_kanban_avatar" alt="Employee"/>
                                <field name="hr_icon_display" class="employee_status_kanban" widget="hr_presence_status"/>
                            </div>
                            <div class="employee_quick_actions">
                                <a name="%(mail.action_discuss)d" type="action" title="إرسال رسالة">
                                    <i class="fa fa-comment-o"/>
                                </a>
                            </div>
                        </div>
                        
                        <div class="employee_card_body">
                            <div class="employee_name_kanban">
                                <field name="name"/>
                            </div>
                            <div class="employee_job_kanban">
                                <field name="job_title"/>
                            </div>
                            <div class="employee_department_kanban">
                                <i class="fa fa-users"/> <field name="department_id"/>
                            </div>
                            <div class="employee_contact_kanban">
                                <div t-if="record.work_email.raw_value" class="contact_item">
                                    <i class="fa fa-envelope-o"/> 
                                    <field name="work_email"/>
                                </div>
                                <div t-if="record.work_phone.raw_value" class="contact_item">
                                    <i class="fa fa-phone"/> 
                                    <field name="work_phone"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="employee_card_footer">
                            <field name="category_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

        <!-- واجهة قائمة محسنة للموظفين -->
        <record id="view_employee_tree_custom" model="ir.ui.view">
            <field name="name">hr.employee.tree.custom</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name="class">employee_list_custom</attribute>
                    <attribute name="decoration-muted">not active</attribute>
                    <attribute name="decoration-bf">newly_hired</attribute>
                </xpath>
                
                <!-- إضافة أعمدة جديدة -->
                <field name="name" position="before">
                    <field name="avatar_128" widget="image" class="employee_list_avatar"/>
                </field>
                
                <field name="work_email" position="after">
                    <field name="hr_icon_display" widget="hr_presence_status" string="الحالة"/>
                </field>
            </field>
        </record>

        <!-- واجهة بحث محسنة -->
        <record id="view_employee_filter_custom" model="ir.ui.view">
            <field name="name">hr.employee.search.custom</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_filter"/>
            <field name="arch" type="xml">
                <!-- إضافة فلاتر جديدة -->
                <filter name="newly_hired" position="after">
                    <filter name="has_manager" string="لديه مدير" domain="[('parent_id', '!=', False)]"/>
                    <filter name="no_manager" string="بدون مدير" domain="[('parent_id', '=', False)]"/>
                    <separator/>
                    <filter name="with_photo" string="لديه صورة" domain="[('image_1920', '!=', False)]"/>
                    <filter name="no_photo" string="بدون صورة" domain="[('image_1920', '=', False)]"/>
                </filter>
                
                <!-- إضافة تجميعات جديدة -->
                <filter name="group_category_ids" position="after">
                    <filter name="group_work_location" string="موقع العمل" domain="[]" context="{'group_by': 'work_location_id'}"/>
                    <filter name="group_coach" string="المدرب" domain="[]" context="{'group_by': 'coach_id'}"/>
                </filter>
            </field>
        </record>

        <!-- إجراء مخصص لفتح واجهة الموظف المحسنة -->
        <record id="open_view_employee_list_custom" model="ir.actions.act_window">
            <field name="name">الموظفون - العرض المحسن</field>
            <field name="res_model">hr.employee</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="view_id" ref="hr_kanban_view_employees_custom"/>
            <field name="search_view_id" ref="view_employee_filter_custom"/>
            <field name="context">{
                'search_default_my_department': 1,
                'chat_icon': True,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    إنشاء موظف جديد
                </p>
                <p>
                    أضف موظفين جدد لإدارة معلوماتهم وتنظيم هيكل الشركة.
                </p>
            </field>
        </record>

        <!-- إضافة عنصر قائمة للواجهة المحسنة -->
        <menuitem id="menu_hr_employee_custom"
                  name="الموظفون - محسن"
                  parent="hr.menu_hr_employee_user"
                  action="open_view_employee_list_custom"
                  sequence="5"/>

    </data>
</odoo>