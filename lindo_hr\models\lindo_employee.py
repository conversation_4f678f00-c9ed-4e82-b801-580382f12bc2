from odoo import _, api, fields, models


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # joining_date = fields.Date(string='Joining Date',
    #                            help="Employee joining date computed from the"
    #                                 " contract start date", store=True,
    #                            compute='_compute_joining_date')

    # @api.depends('contract_id')
    # def _compute_joining_date(self):
    #     for employee in self:
    #         employee.joining_date = min(employee.contract_id.mapped('date_start')) \
    #             if employee.contract_id else False
