/* تصميم مخصص لواجهات الموظفين المحسنة */

/* تصميم واجهة النموذج المخصصة */
.hr_custom_employee_form {
    .employee_header_section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .employee_info_card {
            display: flex;
            align-items: center;
            gap: 30px;
            position: relative;
            z-index: 1;
            
            @media (max-width: 768px) {
                flex-direction: column;
                text-align: center;
            }
        }
        
        .employee_avatar_section {
            .avatar_container {
                position: relative;
                display: inline-block;
                
                .employee_avatar_img {
                    width: 150px;
                    height: 150px;
                    border-radius: 50%;
                    border: 5px solid rgba(255, 255, 255, 0.8);
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    transition: all 0.3s ease;
                    object-fit: cover;
                    
                    &:hover {
                        transform: scale(1.05);
                        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
                    }
                }
                
                .employee_status_indicator {
                    position: absolute;
                    bottom: 10px;
                    right: 10px;
                    background: white;
                    border-radius: 50%;
                    padding: 5px;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                }
            }
        }
        
        .employee_details_section {
            flex: 1;
            color: white;
            
            .employee_name_section {
                .employee_name {
                    font-size: 2.5rem;
                    font-weight: 700;
                    margin-bottom: 10px;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    
                    .chat_icon_container {
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        padding: 10px;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            background: rgba(255, 255, 255, 0.3);
                            transform: scale(1.1);
                        }
                    }
                    
                    input {
                        background: transparent;
                        border: none;
                        color: white;
                        font-size: inherit;
                        font-weight: inherit;
                        
                        &::placeholder {
                            color: rgba(255, 255, 255, 0.7);
                        }
                        
                        &:focus {
                            outline: none;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 10px;
                            padding: 5px 10px;
                        }
                    }
                }
                
                .employee_job_title {
                    font-size: 1.5rem;
                    font-weight: 500;
                    margin-bottom: 15px;
                    opacity: 0.9;
                    
                    input {
                        background: transparent;
                        border: none;
                        color: white;
                        font-size: inherit;
                        font-weight: inherit;
                        
                        &::placeholder {
                            color: rgba(255, 255, 255, 0.6);
                        }
                        
                        &:focus {
                            outline: none;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 8px;
                            padding: 5px 10px;
                        }
                    }
                }
                
                .employee_tags {
                    .o_field_many2manytags {
                        .o_tag {
                            background: rgba(255, 255, 255, 0.2);
                            color: white;
                            border: 1px solid rgba(255, 255, 255, 0.3);
                            border-radius: 20px;
                            padding: 8px 15px;
                            margin: 3px;
                            backdrop-filter: blur(10px);
                            transition: all 0.3s ease;
                            
                            &:hover {
                                background: rgba(255, 255, 255, 0.3);
                                transform: translateY(-2px);
                            }
                        }
                    }
                }
            }
        }
    }
    
    .employee_basic_info_section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
        
        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
        
        .info_card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            
            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            }
            
            .card_title {
                color: #2c3e50;
                font-size: 1.3rem;
                font-weight: 600;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #ecf0f1;
                display: flex;
                align-items: center;
                gap: 10px;
                
                i {
                    color: #3498db;
                    font-size: 1.2rem;
                }
            }
            
            .info_grid {
                display: grid;
                gap: 15px;
                
                .info_item {
                    .info_label {
                        display: block;
                        color: #7f8c8d;
                        font-weight: 600;
                        font-size: 0.9rem;
                        margin-bottom: 5px;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }
                    
                    .info_field {
                        input, select, .o_field_widget {
                            width: 100%;
                            padding: 12px 15px;
                            border: 2px solid #ecf0f1;
                            border-radius: 10px;
                            font-size: 1rem;
                            transition: all 0.3s ease;
                            background: #f8f9fa;
                            
                            &:focus {
                                border-color: #3498db;
                                background: white;
                                box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
                                outline: none;
                            }
                            
                            &:hover {
                                border-color: #bdc3c7;
                            }
                        }
                    }
                }
            }
        }
    }
}

/* تصميم كانبان مخصص */
.o_hr_employee_kanban_custom {
    .o_kanban_renderer {
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        
        .employee_kanban_card {
            background: white;
            border-radius: 20px;
            padding: 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            border: none;
            
            &:hover {
                transform: translateY(-10px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }
            
            .employee_card_header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 20px;
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                
                .employee_avatar_kanban {
                    position: relative;
                    
                    .oe_kanban_avatar {
                        width: 80px;
                        height: 80px;
                        border-radius: 50%;
                        border: 4px solid rgba(255, 255, 255, 0.8);
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                        object-fit: cover;
                    }
                    
                    .employee_status_kanban {
                        position: absolute;
                        bottom: -5px;
                        right: -5px;
                        background: white;
                        border-radius: 50%;
                        padding: 3px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    }
                }
                
                .employee_quick_actions {
                    display: flex;
                    gap: 10px;
                    
                    a {
                        background: rgba(255, 255, 255, 0.2);
                        color: white;
                        width: 35px;
                        height: 35px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            background: rgba(255, 255, 255, 0.3);
                            transform: scale(1.1);
                        }
                    }
                }
            }
            
            .employee_card_body {
                padding: 20px;
                
                .employee_name_kanban {
                    font-size: 1.3rem;
                    font-weight: 700;
                    color: #2c3e50;
                    margin-bottom: 8px;
                }
                
                .employee_job_kanban {
                    font-size: 1rem;
                    color: #7f8c8d;
                    font-weight: 500;
                    margin-bottom: 12px;
                }
                
                .employee_department_kanban {
                    color: #3498db;
                    font-weight: 600;
                    margin-bottom: 15px;
                    
                    i {
                        margin-right: 5px;
                    }
                }
                
                .employee_contact_kanban {
                    .contact_item {
                        color: #7f8c8d;
                        font-size: 0.9rem;
                        margin-bottom: 5px;
                        
                        i {
                            margin-right: 8px;
                            width: 15px;
                            color: #3498db;
                        }
                    }
                }
            }
            
            .employee_card_footer {
                padding: 15px 20px;
                background: #f8f9fa;
                border-top: 1px solid #ecf0f1;
                
                .o_field_many2manytags {
                    .o_tag {
                        background: linear-gradient(45deg, #3498db, #2980b9);
                        color: white;
                        border-radius: 15px;
                        padding: 4px 12px;
                        font-size: 0.8rem;
                        margin: 2px;
                        border: none;
                    }
                }
            }
        }
    }
}

/* تصميم القائمة المخصصة */
.employee_list_custom {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    
    thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        
        th {
            color: white;
            font-weight: 600;
            padding: 20px 15px;
            border: none;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }
    }
    
    tbody {
        tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #ecf0f1;
            
            &:hover {
                background: linear-gradient(90deg, rgba(52, 152, 219, 0.05) 0%, rgba(155, 89, 182, 0.05) 100%);
                transform: scale(1.01);
            }
            
            td {
                padding: 15px;
                border: none;
                vertical-align: middle;
                
                &.employee_list_avatar {
                    .o_field_image {
                        img {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            border: 2px solid #ecf0f1;
                            object-fit: cover;
                        }
                    }
                }
            }
        }
    }
}

/* تحسينات إضافية للتفاعل */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

.employee_status_indicator,
.employee_status_kanban {
    animation: pulse 2s infinite;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .hr_custom_employee_form {
        .employee_basic_info_section {
            .info_card {
                padding: 20px;
                
                .card_title {
                    font-size: 1.1rem;
                }
                
                .info_grid {
                    .info_item {
                        .info_field {
                            input, select {
                                padding: 10px 12px;
                                font-size: 0.9rem;
                            }
                        }
                    }
                }
            }
        }
    }
    
    .o_hr_employee_kanban_custom {
        .employee_kanban_card {
            .employee_card_header {
                padding: 15px;
                
                .employee_avatar_kanban {
                    .oe_kanban_avatar {
                        width: 60px;
                        height: 60px;
                    }
                }
            }
            
            .employee_card_body {
                padding: 15px;
                
                .employee_name_kanban {
                    font-size: 1.1rem;
                }
            }
        }
    }
}

/* تأثيرات تحميل جميلة */
.o_form_view.hr_custom_employee_form,
.o_kanban_view.o_hr_employee_kanban_custom,
.o_list_view .employee_list_custom {
    opacity: 0;
    animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}