<odoo>
    <data>
        <!-- Password Expiry Field View -->
        <record id="view_general_settings_form_inherit" model="ir.ui.view">
            <field name="name">general.settings.config.parameter.inherit</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@id='invite_users']" position="after">
                    <!--                    <field name="password_expire_in_days" invisible="1"/>-->
                    <div id="password_expire">
                        <h2>Set Password Expire Days</h2>
                        <div class="row mt16 o_settings_container" name="users_setting_container">
                            <div class="col-12 col-lg-6 o_setting_box" id="invite_users_setting">
                                <div class="o_setting_left_pane">
                                    <field name="password_expire_enable"/>
                                </div>
                                <div class="o_setting_right_pane" >
                                    <label for="password_expire_enable" class="col-sm-5 col-form-label">
                                        Enable Password Expiration
                                    </label>
                                </div>
                                <div class="o_setting_right_pane" invisible="not password_expire_enable">
                                    <label for="password_expire_in_days" class="col-sm-5 col-form-label">
                                        Password Expiry
                                    </label>
                                    <field name="password_expire_in_days"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>