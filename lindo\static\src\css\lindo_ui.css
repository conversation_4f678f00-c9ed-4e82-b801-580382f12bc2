/* Lindo Modern UI Styles - Enhanced Odoo Classes */

/* Enhanced Odoo Main Navbar */
.o_main_navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    border-bottom: none !important;
    width: 100% !important;
    height: 70px !important;
}

.o_main_navbar.scrolled {
    background: rgba(102, 126, 234, 0.95) !important;
    backdrop-filter: blur(10px);
}

/* Enhanced Navbar Brand */
.o_menu_brand {
    color: white !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.o_menu_brand:hover {
    color: #f0f0f0 !important;
    transform: translateY(-1px);
}

/* Enhanced Navbar Apps Menu */
.o_navbar_apps_menu .dropdown-toggle {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    color: white !important;
}

.o_navbar_apps_menu .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Menu Sections */
.o_menu_sections .o_nav_entry,
.o_menu_sections .dropdown-toggle {
    color: white !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
    margin: 0 5px !important;
}

.o_menu_sections .o_nav_entry:hover,
.o_menu_sections .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.o_menu_sections .dropdown.show > .dropdown-toggle {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
}

/* Enhanced Systray */
.o_menu_systray .o_nav_entry,
.o_menu_systray .dropdown-toggle {
    color: white !important;
    border-radius: 50% !important;
    width: 80px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
}

.o_menu_systray .o_nav_entry:hover,
.o_menu_systray .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1);
}

/* Enhanced Dropdown Menus */
.o_main_navbar .dropdown-menu {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px);
    margin-top: 8px !important;
}

.o_main_navbar .dropdown-item {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    transition: all 0.3s ease !important;
}

.o_main_navbar .dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateX(5px);
}

/* Enhanced Odoo Web Client */
.o_web_client {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Enhanced Action Manager */
.o_action_manager {
    background: transparent !important;
}

.o_action_manager > .o_view_controller {
    background: white !important;
    border-radius: 12px !important;
    margin: 20px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

/* Enhanced Control Panel */
.o_control_panel {
    background: white !important;
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid #e9ecef !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.o_control_panel .breadcrumb {
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
}

.o_control_panel .breadcrumb-item {
    font-weight: 500 !important;
}

.o_control_panel .breadcrumb-item.active {
    color: #667eea !important;
    font-weight: 600 !important;
}

/* Enhanced Search Panel */
.o_search_panel {
    background: white !important;
    border-radius: 12px !important;
    margin: 20px 0 20px 20px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.o_search_panel_section_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    margin: 10px !important;
    padding: 12px 16px !important;
}

.o_search_panel_category_value,
.o_search_panel_filter_value {
    border-radius: 6px !important;
    margin: 2px 10px !important;
    transition: all 0.3s ease !important;
}

.o_search_panel_category_value:hover,
.o_search_panel_filter_value:hover {
    background: #f8f9fa !important;
    transform: translateX(5px);
}

.o_search_panel_category_value.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* Main Content Area */
.lindo-main-content {
    margin-left: 280px;
    margin-top: 60px;
    padding: 30px;
    min-height: calc(100vh - 60px);
    background: #f8f9fa;
    transition: margin-left 0.3s ease;
}

.lindo-sidebar.collapsed + .lindo-main-content {
    margin-left: 70px;
}

/* Content Cards */
.lindo-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.lindo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.lindo-card-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.lindo-card-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.lindo-card-body {
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lindo-navbar-nav {
        display: none;
    }

    .lindo-navbar-toggle {
        display: block;
    }

    .lindo-sidebar {
        transform: translateX(-100%);
    }

    .lindo-sidebar.mobile-open {
        transform: translateX(0);
    }

    .lindo-main-content {
        margin-left: 0;
        padding: 20px;
    }

    .lindo-sidebar.collapsed + .lindo-main-content {
        margin-left: 0;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Custom scrollbar for sidebar */
.lindo-sidebar::-webkit-scrollbar {
    width: 6px;
}

.lindo-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.lindo-sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.lindo-sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

