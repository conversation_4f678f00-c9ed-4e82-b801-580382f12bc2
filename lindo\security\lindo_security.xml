<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="module_category_lindo_system" model="ir.module.category">
            <field name="name">Lindo System</field>
            <field name="sequence">15</field>
        </record>
        <!-- Lindo Hiding Security Group -->
        <record id="group_lindo_hiding" model="res.groups">
            <field name="name">Lindo Hiding</field>
            <field name="category_id" ref="module_category_lindo_system"/>
            <!-- <field name="implied_ids" eval="[(4, ref('base.group_no_one'))]"/> -->
            <field name="comment">Special group for Lindo module with restricted access, automatically assigned to all new users.</field>
        </record>
         <!-- إخفاء القائمة عن الجميع -->
       <!-- <record id="LINDO_menu_root" model="ir.ui.menu">
            <field name="name">Custom Menu</field>
            <field name="groups_id" eval="[(6, 0, [ref('lindo.group_lindo_hiding')])]"/>
        </record> -->

    </data>
</odoo>
