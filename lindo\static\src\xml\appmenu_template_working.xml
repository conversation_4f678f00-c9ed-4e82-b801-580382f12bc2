<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- Lindo Enterprise-Style App Menu Enhancement for Odoo 17 -->
    <!-- WORKING APPROACH: Replace entire Dropdown content -->
    
    <t t-name="web.NavBar.AppsMenu" t-inherit="web.NavBar.AppsMenu" t-inherit-mode="extension">
        <!-- Replace the entire Dropdown component with our Enterprise version -->
        <xpath expr="//Dropdown" position="replace">
            <Dropdown hotkey="'h'" title="'Home Menu'" class="'o_navbar_apps_menu lindo_enterprise_apps'">
                <t t-set-slot="toggler">
                    <i class="oi oi-apps" />
                </t>
                
                <!-- App Drawer Container -->
                <div class="lindo_app_drawer">
                    <!-- Search Bar -->
                    <div class="lindo_app_search_container">
                        <div class="lindo_app_search">
                            <i class="fa fa-search"></i>
                            <input type="text"
                                   class="form-control"
                                   placeholder="Search applications..."
                                   id="lindo-search-input"
                                   autocomplete="off"
                                   role="searchbox"
                                   aria-label="Search applications"/>
                        </div>
                    </div>
                    
                    <!-- Apps Grid Container -->
                    <div class="lindo_apps_grid_container">
                        <div class="lindo_apps_grid" id="lindo-apps-grid" role="grid" aria-label="Applications grid">
                            <t t-foreach="apps" t-as="app" t-key="app.id">
                                <DropdownItem
                                    class="'lindo_app_item'"
                                    href="getMenuItemHref(app)"
                                    dataset="{ menuXmlid: app.xmlid, section: app.id }"
                                    onSelected="() => this.onNavBarDropdownItemSelection(app)"
                                    parentClosingMode="'none'">
                                    
                                    <div class="lindo_app_card"
                                         t-att-data-app-id="app.id"
                                         tabindex="0"
                                         role="button"
                                         t-att-aria-label="'Open ' + app.name + ' application'">
                                        <!-- App Icon -->
                                        <div class="lindo_app_icon">
                                            <img t-if="app.webIconData"
                                                 t-att-src="app.webIconData"
                                                 t-att-alt="app.name + ' icon'"
                                                 loading="lazy"/>
                                            <img t-else=""
                                                 src="/base/static/description/icon.png"
                                                 t-att-alt="app.name + ' icon'"
                                                 loading="lazy"/>
                                        </div>
                                        
                                        <!-- App Name -->
                                        <div class="lindo_app_name" t-esc="app.name"></div>
                                    </div>
                                </DropdownItem>
                            </t>
                        </div>
                    </div>
                </div>
            </Dropdown>
        </xpath>
    </t>

</templates>
