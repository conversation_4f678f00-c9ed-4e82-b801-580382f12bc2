<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">
    <record model="ir.cron" id="cron_update_users">
        <field name='name'>Update user profiles</field>
        <field name='interval_number'>3</field>
        <field name='interval_type'>hours</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall" eval="False" />
        <field name="model_id" ref="ks_access_manager_ninja.model_user_profiles" />
        <field name="state">code</field>
        <field name="code">model.ks_cron_update_users()</field>
    </record>
</odoo>
