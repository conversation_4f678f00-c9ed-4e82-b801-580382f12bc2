<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="10197f47-c9a6-4d38-b8c9-1967bc398d01" name="Changes" comment="mn">
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/.vscode/settings.json" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/addons/rest_api_all_models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/addons/rest_api_all_models/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/addons/rest_api_all_models/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/addons/rest_api_all_models/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/addons/rest_api_all_models/models/api_model.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/README.rst" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/controllers/patient_booking.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/controllers/portal.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/controllers/view_portal.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/data/ir_cron_data.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/data/ir_sequence_data.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/data/website_data.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/demo/hr_job_demo.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/doc/RELEASE_NOTES.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/account_payment_register.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/blood_bank.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/blood_donation.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/contra_indication.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/doctor_allocation.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/doctor_round.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/doctor_slot.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/doctor_specialization.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_bed.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_building.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_degree.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_family.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_inpatient.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_insurance.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_laboratory.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_outpatient.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_pharmacy.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_vaccination.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hospital_ward.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/hr_employee.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/inpatient_payment.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/inpatient_surgery.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/ir_attachment.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/lab_medicine_line.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/lab_test.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/lab_test_line.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/lab_test_result.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/medicine_brand.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/nursing_plan.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/patient_lab_test.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/patient_room.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/pharmacy_medicine.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/prescription_line.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/product_template.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/res_partner.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/res_users.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/models/room_facility.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/report/lab_test_line_reports.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/report/res_partner_reports.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/security/base_hospital_management_groups.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/security/doctor_allocation_security.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/security/doctor_slot_security.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/security/patient_booking_security.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/security/patient_lab_test_security.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/capture (1).png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/check.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/chevron.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/cogs.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/consultation.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/ecom-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/education-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/hotel-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/img.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/license.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/lifebuoy.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/manufacturing-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/photo-capture.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/pos-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/puzzle.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/restaurant-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/service-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/trading-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/training.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/update.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/user.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/icons/wrench.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/Cybrosys R.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/categories.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/check-box.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/compass.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/corporate.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/customer-support.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/cybrosys-logo.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/email.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/features.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/logo.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/phone.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/pictures.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/pie-chart.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/right-arrow.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/star (1) 2.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/star.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/support (1) 1.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/support-email.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/support.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/tick-mark.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/whatsapp 1.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/whatsapp.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/misc/whatsapp.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/1.gif" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/1.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/2.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/3.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/4.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/5.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/modules/6.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h1.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h10.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h11.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h12.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h13.pdf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h14.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h15.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h16.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h17.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h18.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h19.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h2.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h20.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h21.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h22.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h23.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h24.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h25.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h26.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h27.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h28.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h29.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h3.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h30.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h31.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h32.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h33.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h34.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h35.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h36.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h37.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h38.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h39.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h4.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h40.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h41.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h42.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h43.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h44.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h45.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h5.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h6.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h7.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h8.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/h9.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots/hero.gif" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1.zip" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h19.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h2.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h20.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h21.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h22.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h3.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h36.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h37.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h38.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h39.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h4.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/assets/screenshots1/h5.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/banner.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/icon.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/description/index.html" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/css/doctor_dashboard.css" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/css/lab_dashboard.css" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/css/pharmacy_dashboard.css" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/css/reception_dashboard.css" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/img/in_p.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/img/lab_test.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/img/medicine.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/img/op_ticket.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/img/out_p.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/img/vaccination1.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/doctor_dashboard.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/lab_dashboard.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/pharmacy_dashboard.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/pharmacy_orderlines.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/prescription.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/reception_dashboard.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/js/website_page.js" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/xml/doctor_dashboard_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/xml/lab_dashboard_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/xml/pharmacy_dashboard_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/xml/pharmacy_orderlines.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/static/src/xml/reception_dashboard_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/blood_bank_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/booking_success_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/contra_indication_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/doctor_allocation_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/doctor_slot_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/doctor_specialization_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_bed_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_building_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_degree_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_inpatient_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_insurance_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_laboratory_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_outpatient_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_pharmacy_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_vaccination_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hospital_ward_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/hr_employee_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/inpatient_surgery_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/lab_test_line_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/lab_test_result_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/lab_test_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/medicine_brand_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/menu_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/patient_booking_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/patient_card_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/patient_lab_test_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/patient_portal_templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/patient_room_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/product_template_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/res_partner_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/base_hospital_management/views/room_facility_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/README.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/controllers/main.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/models/ir_http.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/cors_middleware/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/goverment_hr/views/administrative_entity_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/goverment_hr/views/employee_transfer_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/goverment_hr/views/employee_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/goverment_hr/views/entity_type_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/goverment_hr/views/job_position_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/goverment_hr/views/menuitems.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/README.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/controllers/main.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/demo/demo_data.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/models/travel_expense.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/models/travel_request.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/static/description/icon.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/views/menu_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/views/templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/views/travel_expense_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/govtravel/views/travel_request_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/install_cors_middleware.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/controllers/controllers.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/data/mini_hr_custom_data.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/demo/demo.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/demo/mini_hr_custom_demo.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/hr_employee.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/hr_employee_attendance.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/hr_employee_certification.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/hr_employee_document.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/hr_employee_education.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/hr_employee_emergency_contact.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/models/models.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/security/mini_hr_custom_security.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/hr_employee_attendance_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/hr_employee_certification_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/hr_employee_document_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/hr_employee_education_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/hr_employee_emergency_contact_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/hr_employee_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/menu_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/templates.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/views/views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/wizards/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/wizards/import_employee_data.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/mini_hr_custom/wizards/import_employee_data_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/muk_web_appsbar/static/src/webclient/webclient.scss" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/description/banner.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/description/icon.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/description/images/sample.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/description/index.html" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/css/pos_style.css" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/css/web_style.css" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Bold.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Bold.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Bold.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Bold.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Bold.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Extrabold.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Extrabold.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Extrabold.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Extrabold.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Extrabold.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Light.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Light.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Light.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Light.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Light.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Regular.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Regular.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Regular.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Regular.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Almarai/Almarai-Regular.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Black.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Black.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Black.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Black.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Bold.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Bold.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Bold.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Bold.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-BoldItalic.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-BoldItalic.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-BoldItalic.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-BoldItalic.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLight.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLight.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLight.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLight.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLightItalic.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLightItalic.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLightItalic.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-ExtraLightItalic.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Italic.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Italic.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Italic.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Italic.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Light.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Light.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Light.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Light.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-LightItalic.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-LightItalic.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-LightItalic.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-LightItalic.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Regular.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Regular.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Regular.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-Regular.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBold.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBold.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBold.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBold.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBoldItalic.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBoldItalic.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBoldItalic.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/Cairo/Cairo-SemiBoldItalic.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Bold.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Bold.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Bold.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Bold.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Regular.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Regular.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Regular.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidKufi-Regular.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Bold.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Bold.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Bold.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Bold.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Regular.eot" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Regular.ttf" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Regular.woff" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/fonts/droid/DroidNaskh-Regular.woff2" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/scss/almaraifont.scss" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/scss/cairofont.scss" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/static/src/scss/droidfont.scss" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nati_arabic_font/views/linkestatic.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/README.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/auth.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/discussion.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/hr.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/main.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/messaging.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/controllers/task.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/models/api_token.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/views/api_token_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/nuxt_api/views/menu_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/CUSTOM_MODULES_GUIDE.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/INSTALLATION.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/README.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/controllers/main.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/models/api_log.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/models/api_model.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/models/api_rule.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/models/models.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/models/res_users.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/register_custom_modules.bat" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/register_custom_modules.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/register_models.bat" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/register_models.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/setup.bat" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/setup.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/test_api.bat" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/scripts/test_api.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/static/description/index.html" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/views/api_log_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/views/api_model_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/views/api_rule_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/views/menu_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_all_models/views/res_users_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/Postman Collections/Odoo REST Api.postman_collection.json" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/Postman Collections/Odoo REST Api.postman_collection.zip" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/README.rst" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/__manifest__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/controllers/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/controllers/rest_api_odoo.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/doc/RELEASE_NOTES.md" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/models/__init__.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/models/connection_api.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/models/res_users.py" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/security/ir.model.access.csv" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/check.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/chevron.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/cogs.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/consultation.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/ecom-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/education-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/hotel-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/license.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/lifebuoy.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/logo.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/manufacturing-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/pos-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/puzzle.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/restaurant-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/service-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/trading-black.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/training.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/update.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/user.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/icons/wrench.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/Cybrosys R.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/email.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/phone.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/star (1) 2.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/support (1) 1.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/support-email.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/tick-mark.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/whatsapp 1.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/misc/whatsapp.svg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/modules/1.gif" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/modules/2.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/modules/3.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/modules/4.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/modules/5.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/modules/6.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/1.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/10.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/11.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/12.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/13.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/14.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/15.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/16.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/17.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/18.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/19.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/2.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/20.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/21.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/22.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/23.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/24.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/3.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/4.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/5.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/6.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/7.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/8.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/9.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/assets/screenshots/hero.gif" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/banner.jpg" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/icon.png" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/static/description/index.html" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/views/connection_api_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/rest_api_odoo/views/res_users_views.xml" afterDir="false" />
      <change afterPath="$USER_HOME$/PycharmProjects/custom-modules-v17/test_cors.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <branch-grouping />
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="mohamedAlamoudi &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$USER_HOME$/PycharmProjects/custom-modules-v17" value="moutazApi" />
        <entry key="$PROJECT_DIR$/odoorpc" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$USER_HOME$/PycharmProjects/custom-modules-v17" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectId" id="2eh2CikGMP7bLbcfH7g6PsO0bqY" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "ASKED_MARK_IGNORED_FILES_AS_EXCLUDED": "true",
    "Notification.DisplayName-DoNotAsk-Vcs Important Messages": "VCS important messages",
    "Notification.DoNotAsk-Vcs Important Messages": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "eng-moutaz",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/PycharmProjects/custom-modules-v17",
    "run.code.analysis.last.selected.profile": "aDefault",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\PycharmProjects\custom-modules-v17\custom_invoices\controllers" />
      <recent name="C:\odoo\odoo-17.0\venv" />
      <recent name="C:\Users\<USER>\PycharmProjects\custom-modules-v17\custom_access\models" />
      <recent name="C:\Users\<USER>\PycharmProjects\custom-modules-v17\custom_access\security" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\PycharmProjects\custom-modules-v17" />
      <recent name="C:\odoo\odoo-17.0" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="v17" type="PythonConfigurationType" factoryName="Python">
      <module name="odoo-17.0" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="C:\odoo\odoo-17.0\venv\Scripts\python.exe" />
      <option name="SDK_NAME" value="Python 3.11 (odoo-17.0)" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/odoo-bin" />
      <option name="PARAMETERS" value="--config=C:\odoo\odoo-17.0\odoo.conf -u rest_api_odoo" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="10197f47-c9a6-4d38-b8c9-1967bc398d01" name="Changes" comment="" />
      <created>1712342363897</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1712342363897</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠١" summary="n">
      <created>1712616116634</created>
      <option name="number" value="٠٠٠٠١" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠١" />
      <option name="project" value="LOCAL؜" />
      <updated>1712616116634</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٢" summary="n">
      <created>1715973498379</created>
      <option name="number" value="٠٠٠٠٢" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٢" />
      <option name="project" value="LOCAL؜" />
      <updated>1715973498397</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٣" summary="n">
      <created>1716228550626</created>
      <option name="number" value="٠٠٠٠٣" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٣" />
      <option name="project" value="LOCAL؜" />
      <updated>1716228550626</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٤" summary="n">
      <created>1716228667521</created>
      <option name="number" value="٠٠٠٠٤" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٤" />
      <option name="project" value="LOCAL؜" />
      <updated>1716228667521</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٥" summary="n">
      <created>1716318968194</created>
      <option name="number" value="٠٠٠٠٥" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٥" />
      <option name="project" value="LOCAL؜" />
      <updated>1716318968194</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٦" summary="n">
      <created>1716319449080</created>
      <option name="number" value="٠٠٠٠٦" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٦" />
      <option name="project" value="LOCAL؜" />
      <updated>1716319449080</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٧" summary="n">
      <created>1716329501652</created>
      <option name="number" value="٠٠٠٠٧" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٧" />
      <option name="project" value="LOCAL؜" />
      <updated>1716329501652</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٨" summary="n">
      <created>1716517189625</created>
      <option name="number" value="٠٠٠٠٨" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٨" />
      <option name="project" value="LOCAL؜" />
      <updated>1716517189696</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٠٩" summary="n">
      <created>*************</created>
      <option name="number" value="٠٠٠٠٩" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٠٩" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٠" summary="n">
      <created>*************</created>
      <option name="number" value="٠٠٠١٠" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٠" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١١" summary="translation in custom_invoices\models , hraj_accounting_reports , hraj_marketing\models , report_xlsx\i18n and add vehicle_pos_order">
      <created>*************</created>
      <option name="number" value="٠٠٠١١" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١١" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٢" summary="n">
      <created>*************</created>
      <option name="number" value="٠٠٠١٢" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٢" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٣" summary="n">
      <created>*************</created>
      <option name="number" value="٠٠٠١٣" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٣" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٤" summary="n">
      <created>1717033218639</created>
      <option name="number" value="٠٠٠١٤" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٤" />
      <option name="project" value="LOCAL؜" />
      <updated>1717033218639</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٥" summary="n">
      <created>*************</created>
      <option name="number" value="٠٠٠١٥" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٥" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٦" summary="n">
      <created>*************</created>
      <option name="number" value="٠٠٠١٦" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٦" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٧" summary="translation in custom_invoices\models , hraj_accounting_reports , hraj_marketing\models , report_xlsx\i18n and add vehicle_pos_order">
      <created>*************</created>
      <option name="number" value="٠٠٠١٧" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٧" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٨" summary="translation in custom_invoices\models , hraj_accounting_reports , hraj_marketing\models , report_xlsx\i18n and add vehicle_pos_order">
      <created>*************</created>
      <option name="number" value="٠٠٠١٨" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٨" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠١٩" summary="translation in custom_invoices\models , hraj_accounting_reports , hraj_marketing\models , report_xlsx\i18n and add vehicle_pos_order">
      <created>*************</created>
      <option name="number" value="٠٠٠١٩" />
      <option name="presentableId" value="LOCAL؜-٠٠٠١٩" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٠" summary="mn">
      <created>*************</created>
      <option name="number" value="٠٠٠٢٠" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٠" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢١" summary="mn">
      <created>*************</created>
      <option name="number" value="٠٠٠٢١" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢١" />
      <option name="project" value="LOCAL؜" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٢" summary="mn">
      <created>1717876081124</created>
      <option name="number" value="٠٠٠٢٢" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٢" />
      <option name="project" value="LOCAL؜" />
      <updated>1717876081124</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٣" summary="mn">
      <created>1717876708092</created>
      <option name="number" value="٠٠٠٢٣" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٣" />
      <option name="project" value="LOCAL؜" />
      <updated>1717876708092</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٤" summary="mn">
      <created>1717876836584</created>
      <option name="number" value="٠٠٠٢٤" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٤" />
      <option name="project" value="LOCAL؜" />
      <updated>1717876836584</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٥" summary="mn">
      <created>1717970203722</created>
      <option name="number" value="٠٠٠٢٥" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٥" />
      <option name="project" value="LOCAL؜" />
      <updated>1717970203723</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٦" summary="mn">
      <created>1717970484486</created>
      <option name="number" value="٠٠٠٢٦" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٦" />
      <option name="project" value="LOCAL؜" />
      <updated>1717970484486</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٧" summary="mn">
      <created>1717976207010</created>
      <option name="number" value="٠٠٠٢٧" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٧" />
      <option name="project" value="LOCAL؜" />
      <updated>1717976207010</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٨" summary="mn">
      <created>1717976218783</created>
      <option name="number" value="٠٠٠٢٨" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٨" />
      <option name="project" value="LOCAL؜" />
      <updated>1717976218783</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٢٩" summary="mn">
      <created>1718121026748</created>
      <option name="number" value="٠٠٠٢٩" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٢٩" />
      <option name="project" value="LOCAL؜" />
      <updated>1718121026748</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٠" summary="mn">
      <created>1718134192548</created>
      <option name="number" value="٠٠٠٣٠" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٠" />
      <option name="project" value="LOCAL؜" />
      <updated>1718134192548</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣١" summary="mn">
      <created>1718134952046</created>
      <option name="number" value="٠٠٠٣١" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣١" />
      <option name="project" value="LOCAL؜" />
      <updated>1718134952046</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٢" summary="mn">
      <created>1718135223634</created>
      <option name="number" value="٠٠٠٣٢" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٢" />
      <option name="project" value="LOCAL؜" />
      <updated>1718135223634</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٣" summary="mn">
      <created>1718135518851</created>
      <option name="number" value="٠٠٠٣٣" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٣" />
      <option name="project" value="LOCAL؜" />
      <updated>1718135518851</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٤" summary="mn">
      <created>1718138411904</created>
      <option name="number" value="٠٠٠٣٤" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٤" />
      <option name="project" value="LOCAL؜" />
      <updated>1718138411904</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٥" summary="mn">
      <created>1718145469582</created>
      <option name="number" value="٠٠٠٣٥" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٥" />
      <option name="project" value="LOCAL؜" />
      <updated>1718145469582</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٦" summary="mn">
      <created>1718147920295</created>
      <option name="number" value="٠٠٠٣٦" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٦" />
      <option name="project" value="LOCAL؜" />
      <updated>1718147920295</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٧" summary="mn">
      <created>1718148926119</created>
      <option name="number" value="٠٠٠٣٧" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٧" />
      <option name="project" value="LOCAL؜" />
      <updated>1718148926119</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٨" summary="mn">
      <created>1718150570406</created>
      <option name="number" value="٠٠٠٣٨" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٨" />
      <option name="project" value="LOCAL؜" />
      <updated>1718150570406</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٣٩" summary="mn">
      <created>1718224345440</created>
      <option name="number" value="٠٠٠٣٩" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٣٩" />
      <option name="project" value="LOCAL؜" />
      <updated>1718224345440</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٤٠" summary="mn">
      <created>1718224927403</created>
      <option name="number" value="٠٠٠٤٠" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٤٠" />
      <option name="project" value="LOCAL؜" />
      <updated>1718224927403</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٤١" summary="mn">
      <created>1718226814263</created>
      <option name="number" value="٠٠٠٤١" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٤١" />
      <option name="project" value="LOCAL؜" />
      <updated>1718226814263</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٤٢" summary="mn">
      <created>1719177971058</created>
      <option name="number" value="٠٠٠٤٢" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٤٢" />
      <option name="project" value="LOCAL؜" />
      <updated>1719177971082</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٤٣" summary="mn">
      <created>1719179136425</created>
      <option name="number" value="٠٠٠٤٣" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٤٣" />
      <option name="project" value="LOCAL؜" />
      <updated>1719179136425</updated>
    </task>
    <task id="LOCAL؜-٠٠٠٤٤" summary="mn">
      <created>1732921282517</created>
      <option name="number" value="٠٠٠٤٤" />
      <option name="presentableId" value="LOCAL؜-٠٠٠٤٤" />
      <option name="project" value="LOCAL؜" />
      <updated>1732921282517</updated>
    </task>
    <option name="localTasksCounter" value="45" />
    <servers />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.conf" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/MohammedAbdullah" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="moutaz-api" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="MohammedAbdullah" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="false" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/MohammedAbdullah" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="n" />
    <MESSAGE value="translation in custom_invoices\models , hraj_accounting_reports , hraj_marketing\models , report_xlsx\i18n and add vehicle_pos_order" />
    <MESSAGE value="mn" />
    <option name="LAST_COMMIT_MESSAGE" value="mn" />
  </component>
</project>