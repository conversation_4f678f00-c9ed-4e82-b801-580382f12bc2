/* تخصيصات جميلة لواجهات تطبيق الموظفين */

/* تحسين بطاقة الموظف الرئيسية */
.o_hr_employee_form_view .o_form_renderer {
    .o_form_sheet_bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
        position: relative;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
        }
        
        > * {
            position: relative;
            z-index: 1;
        }
    }

    /* تحسين صورة الموظف */
    .o_employee_avatar {
        .oe_avatar {
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            
            &:hover {
                transform: scale(1.05);
                box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            }
        }
        
        .o_employee_availability {
            border: 3px solid #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    }

    /* تحسين عنوان الموظف */
    .oe_title {
        h1 {
            color: #2c3e50;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        
        h2 {
            color: #7f8c8d;
            font-weight: 500;
            font-style: italic;
        }
    }

    /* تحسين التاجز */
    .o_field_many2manytags {
        .o_tag {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            margin: 2px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            }
        }
    }

    /* تحسين المجموعات */
    .o_group {
        background: rgba(255,255,255,0.8);
        border-radius: 15px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid rgba(255,255,255,0.2);
        
        .o_group_col_6 {
            padding: 10px;
        }
        
        .o_form_label {
            color: #34495e;
            font-weight: 600;
            font-size: 14px;
        }
        
        .o_field_widget {
            input, select, textarea {
                border-radius: 8px;
                border: 2px solid #ecf0f1;
                padding: 10px 15px;
                transition: all 0.3s ease;
                
                &:focus {
                    border-color: #3498db;
                    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
                    outline: none;
                }
            }
        }
    }

    /* تحسين التبويبات */
    .o_notebook {
        .nav-tabs {
            border: none;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 20px;
            
            .nav-link {
                border: none;
                border-radius: 10px;
                color: #7f8c8d;
                font-weight: 600;
                padding: 12px 25px;
                margin: 0 5px;
                transition: all 0.3s ease;
                
                &.active {
                    background: linear-gradient(45deg, #3498db, #2980b9);
                    color: white;
                    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
                }
                
                &:hover:not(.active) {
                    background: rgba(52, 152, 219, 0.1);
                    color: #3498db;
                }
            }
        }
        
        .tab-content {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
    }
}

/* تحسين عرض الكانبان للموظفين */
.o_hr_employee_kanban .o_kanban_renderer {
    .o_kanban_record {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
        }
        
        > * {
            position: relative;
            z-index: 1;
        }
        
        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.25);
        }
        
        .oe_kanban_avatar {
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .oe_kanban_details {
            .o_kanban_record_title {
                color: #2c3e50;
                font-weight: 700;
                font-size: 16px;
            }
            
            .o_kanban_record_subtitle {
                color: #7f8c8d;
                font-weight: 500;
            }
        }
    }
}

/* تحسين عرض القائمة */
.o_list_view {
    .o_list_table {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        
        thead {
            background: linear-gradient(45deg, #3498db, #2980b9);
            
            th {
                color: white;
                font-weight: 600;
                padding: 15px;
                border: none;
            }
        }
        
        tbody {
            tr {
                transition: all 0.3s ease;
                
                &:hover {
                    background: rgba(52, 152, 219, 0.05);
                    transform: scale(1.01);
                }
                
                td {
                    padding: 12px 15px;
                    border: none;
                    border-bottom: 1px solid #ecf0f1;
                }
            }
        }
    }
}

/* تحسين الأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    
    &.btn-primary {
        background: linear-gradient(45deg, #3498db, #2980b9);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }
    }
    
    &.btn-secondary {
        background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        color: white;
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        }
    }
}

/* تحسين البحث */
.o_searchview {
    .o_searchview_input_container {
        background: rgba(255,255,255,0.9);
        border-radius: 25px;
        border: 2px solid #ecf0f1;
        transition: all 0.3s ease;
        
        &:focus-within {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        input {
            border: none;
            padding: 12px 20px;
            
            &:focus {
                outline: none;
            }
        }
    }
    
    .o_searchview_facet {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        border-radius: 20px;
        border: none;
        
        .o_facet_remove {
            color: white;
        }
    }
}

/* تحسين لوحة البحث */
.o_search_panel {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    margin: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    
    .o_search_panel_section {
        .o_search_panel_section_header {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 15px;
            font-weight: 600;
        }
        
        .o_search_panel_field {
            padding: 10px 15px;
            transition: all 0.3s ease;
            
            &:hover {
                background: rgba(52, 152, 219, 0.05);
            }
            
            &.active {
                background: rgba(52, 152, 219, 0.1);
                color: #3498db;
                font-weight: 600;
            }
        }
    }
}

/* تأثيرات حركية جميلة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.o_form_view, .o_kanban_view, .o_list_view {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسين الرسائل والتنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    padding: 15px 20px;
    
    &.alert-info {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
    }
    
    &.alert-success {
        background: linear-gradient(45deg, #27ae60, #229954);
        color: white;
    }
    
    &.alert-warning {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
    }
    
    &.alert-danger {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
    }
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 10px 0;
    
    .dropdown-item {
        padding: 10px 20px;
        transition: all 0.3s ease;
        
        &:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
    }
}

/* تحسين المودال */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    
    .modal-header {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        border-radius: 20px 20px 0 0;
        border: none;
        
        .modal-title {
            font-weight: 600;
        }
        
        .btn-close {
            filter: brightness(0) invert(1);
        }
    }
    
    .modal-body {
        padding: 30px;
    }
    
    .modal-footer {
        border: none;
        padding: 20px 30px;
    }
}

/* تحسين الباجات والعلامات */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 600;
    
    &.badge-primary {
        background: linear-gradient(45deg, #3498db, #2980b9);
    }
    
    &.badge-success {
        background: linear-gradient(45deg, #27ae60, #229954);
    }
    
    &.badge-warning {
        background: linear-gradient(45deg, #f39c12, #e67e22);
    }
    
    &.badge-danger {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
    }
}

/* تحسين الأيقونات */
.fa, .oi {
    transition: all 0.3s ease;
    
    &:hover {
        transform: scale(1.1);
    }
}

/* تحسين الشريط الجانبي */
.o_cp_sidebar {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    margin: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

/* تحسين شريط التحكم */
.o_control_panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    margin: 10px;
    padding: 20px;
    
    .o_cp_top {
        .breadcrumb {
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 10px 20px;
            
            .breadcrumb-item {
                color: white;
                
                &.active {
                    color: rgba(255,255,255,0.8);
                }
            }
        }
    }
}
