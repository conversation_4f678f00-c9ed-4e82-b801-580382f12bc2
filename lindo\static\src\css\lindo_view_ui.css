/* Button styles */
.lindo-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.lindo-btn-primary {
    background: #667eea;
    color: white;
}

.lindo-btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.lindo-btn-secondary {
    background: #6c757d;
    color: white;
}

.lindo-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Form enhancements */
.lindo-form-group {
    margin-bottom: 20px;
}

.lindo-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.lindo-form-control {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    transition: border-color 0.3s ease;
}

.lindo-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Status indicators */
.lindo-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.lindo-status-success {
    background: #d4edda;
    color: #155724;
}

.lindo-status-warning {
    background: #fff3cd;
    color: #856404;
}

.lindo-status-danger {
    background: #f8d7da;
    color: #721c24;
}

.lindo-status-info {
    background: #d1ecf1;
    color: #0c5460;
}
/* Enhanced Odoo List Views */
.o_list_view {
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.o_list_table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.o_list_table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.o_list_table thead th {
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    letter-spacing: 1px !important;
}

.o_list_table tbody tr {
    transition: all 0.3s ease !important;
    border-bottom: 1px solid #f1f3f4 !important;
}

.o_list_table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
    transform: translateX(5px);
}

.o_list_table tbody td {
    padding: 16px 12px !important;
    border: none !important;
    vertical-align: middle !important;
}

/* Enhanced Odoo Form Views */
.o_form_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

/* .o_form_sheet_bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-radius: 12px !important; */
}

/* .o_form_sheet {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
    margin: 20px !important;
    padding: 30px !important;
} */

/* Enhanced Form Groups */
.o_group {
    margin-bottom: 24px !important;
}

.o_group .o_td_label {
    font-weight: 600 !important;
    color: #495057 !important;
    padding: 12px 16px 12px 0 !important;
}

.o_field_widget {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget input,
.o_field_widget select,
.o_field_widget textarea {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
}

.o_field_widget input:focus,
.o_field_widget select:focus,
.o_field_widget textarea:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* Enhanced Buttons */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

.btn-secondary {
    background: #6c757d !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.btn-secondary:hover {
    background: #5a6268 !important;
    transform: translateY(-2px) !important;
}

/* Enhanced Kanban Views */
.o_kanban_view {
    background: transparent !important;
}

.o_kanban_record {
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    transition: all 0.3s ease !important;
    margin-bottom: 16px !important;
}

.o_kanban_record:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.o_kanban_record .o_kanban_record_title {
    font-weight: 600 !important;
    color: #495057 !important;
}

/* Enhanced Calendar Views */
.o_calendar_view {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.fc-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px 12px 0 0 !important;
}

.fc-toolbar .fc-button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.fc-toolbar .fc-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
}

/* Enhanced Pivot Views */
.o_pivot_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

.o_pivot table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.o_pivot table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
}

/* Enhanced Graph Views */
.o_graph_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 20px !important;
}

/* Enhanced Modal Dialogs */
.modal-content {
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 16px 16px 0 0 !important;
    border-bottom: none !important;
    padding: 24px !important;
}

.modal-title {
    font-weight: 600 !important;
    font-size: 18px !important;
}

.modal-body {
    padding: 24px !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 20px 24px !important;
    border-radius: 0 0 16px 16px !important;
}

/* Enhanced Notifications */
.o_notification {
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
    backdrop-filter: blur(10px) !important;
}

.o_notification.o_notification_success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
}

.o_notification.o_notification_warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: white !important;
}

.o_notification.o_notification_danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    color: white !important;
}

/* Enhanced Status Bar */
.o_statusbar_status {
    border-radius: 8px !important;
    margin: 0 4px !important;
    transition: all 0.3s ease !important;
}

.o_statusbar_status.o_arrow_button_current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* Enhanced Chatter */
.o_chatter {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
}

.o_chatter_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 20px !important;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .o_main_navbar {
        padding: 10px !important;
    }

    .o_action_manager > .o_view_controller {
        margin: 10px !important;
        border-radius: 8px !important;
    }

    .o_form_sheet {
        margin: 10px !important;
        padding: 20px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 12px 8px !important;
        font-size: 13px !important;
    }
}

/* Animation utilities for enhanced UX */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Enhanced Odoo UI - Complete */
.o_web_client .fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.o_web_client .slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Additional CSS classes for enhanced interactions */
.btn.clicked {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
}

.o_field_widget.focused {
    transform: scale(1.02) !important;
    z-index: 10 !important;
}

.o_main_navbar.scrolled {
    background: rgba(102, 126, 234, 0.95) !important;
    backdrop-filter: blur(10px) !important;
}

/* Enhanced hover states */
.o_list_table tbody tr.slide-in-left {
    transform: translateX(5px) !important;
}

.o_kanban_record.fade-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Smooth transitions for all enhanced elements */
.o_main_navbar,
.o_list_table tbody tr,
.o_field_widget,
.o_kanban_record,
.btn {
    transition: all 0.3s ease !important;
}
/* ========================================
   ENHANCED ODOO XML VIEWS - COMPREHENSIVE
   ======================================== */

/* Enhanced Tree View (.o_list_view) */
.o_list_view .o_list_table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.o_list_view .o_list_table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    letter-spacing: 1px !important;
    position: relative !important;
}

.o_list_view .o_list_table thead th:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%) !important;
    transform: translateY(-1px) !important;
}

.o_list_view .o_list_table tbody tr {
    transition: all 0.3s ease !important;
    border-bottom: 1px solid #f1f3f4 !important;
    position: relative !important;
}

.o_list_view .o_list_table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%) !important;
    transform: translateX(8px) !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15) !important;
}

.o_list_view .o_list_table tbody tr.o_selected_row {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%) !important;
    border-left: 4px solid #667eea !important;
}

.o_list_view .o_list_table tbody td {
    padding: 16px 12px !important;
    border: none !important;
    vertical-align: middle !important;
    position: relative !important;
}

/* Enhanced Form View (.o_form_view) */
.o_form_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
    margin: 20px !important;
}

.o_form_view .o_form_sheet_bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-radius: 12px !important;
    padding: 0 !important;
}

.o_form_view .o_form_sheet {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
    margin: 20px !important;
    padding: 30px !important;
    position: relative !important;
}

.o_form_view .o_form_sheet::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 12px 12px 0 0 !important;
}

/* Enhanced Form Groups */
.o_form_view .o_group {
    margin-bottom: 24px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
}

.o_form_view .o_group .o_td_label {
    font-weight: 600 !important;
    color: #495057 !important;
    padding: 12px 16px 12px 0 !important;
    position: relative !important;
}

.o_form_view .o_group .o_td_label::after {
    content: '' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 30px !important;
    height: 2px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 1px !important;
}

/* Enhanced Field Widgets */
.o_form_view .o_field_widget {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.o_form_view .o_field_widget input,
.o_form_view .o_field_widget select,
.o_form_view .o_field_widget textarea {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    background: white !important;
}

.o_form_view .o_field_widget input:focus,
.o_form_view .o_field_widget select:focus,
.o_form_view .o_field_widget textarea:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
    transform: translateY(-1px) !important;
}

.o_form_view .o_field_widget.o_field_highlight {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
    border-radius: 8px !important;
    padding: 8px !important;
}

/* Enhanced Kanban View (.o_kanban_view) */
.o_kanban_view {
    background: transparent !important;
    padding: 20px !important;
}

.o_kanban_view .o_kanban_record {
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    transition: all 0.3s ease !important;
    margin-bottom: 16px !important;
    background: white !important;
    position: relative !important;
    overflow: hidden !important;
}

.o_kanban_view .o_kanban_record::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.o_kanban_view .o_kanban_record:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.o_kanban_view .o_kanban_record .o_kanban_record_title {
    font-weight: 600 !important;
    color: #495057 !important;
    font-size: 16px !important;
    margin-bottom: 8px !important;
}

.o_kanban_view .o_kanban_record .o_kanban_record_subtitle {
    color: #6c757d !important;
    font-size: 14px !important;
}

.o_kanban_view .o_kanban_record .o_dropdown_kanban {
    background: rgba(102, 126, 234, 0.1) !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
}

.o_kanban_view .o_kanban_record .o_dropdown_kanban:hover {
    background: rgba(102, 126, 234, 0.2) !important;
    transform: scale(1.1) !important;
}

/* Enhanced Calendar View (.o_calendar_view) */
.o_calendar_view {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    background: white !important;
}

.o_calendar_view .fc-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px 12px 0 0 !important;
    border: none !important;
}

.o_calendar_view .fc-toolbar .fc-button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    transition: all 0.3s ease !important;
    padding: 8px 16px !important;
}

.o_calendar_view .fc-toolbar .fc-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

.o_calendar_view .fc-event {
    border-radius: 6px !important;
    border: none !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    transition: all 0.3s ease !important;
}

.o_calendar_view .fc-event:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Enhanced Pivot View (.o_pivot_view) */
.o_pivot_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
    margin: 20px !important;
}

.o_pivot_view table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.o_pivot_view table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
    text-align: center !important;
}

.o_pivot_view table tbody td {
    padding: 12px !important;
    border: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_pivot_view table tbody td:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

/* Enhanced Graph View (.o_graph_view) */
.o_graph_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 20px !important;
    margin: 20px !important;
}

.o_graph_view .o_graph_canvas_container {
    border-radius: 8px !important;
    overflow: hidden !important;
}

/* Enhanced Search View (.o_searchview) */
.o_searchview {
    background: white !important;
    border-radius: 25px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
}

.o_searchview:focus-within {
    border-color: #667eea !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important;
    transform: translateY(-1px) !important;
}

.o_searchview .o_searchview_input {
    border: none !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    background: transparent !important;
}

.o_searchview .o_searchview_input::placeholder {
    color: #6c757d !important;
    font-style: italic !important;
}

/* Enhanced Activity View (.o_activity_view) */
.o_activity_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_activity_view .o_activity_record {
    border-radius: 8px !important;
    margin: 10px !important;
    padding: 15px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border-left: 4px solid #667eea !important;
    transition: all 0.3s ease !important;
}

.o_activity_view .o_activity_record:hover {
    background: rgba(102, 126, 234, 0.05) !important;
    transform: translateX(5px) !important;
}

/* Enhanced Gantt View (.o_gantt_view) */
.o_gantt_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_gantt_view .o_gantt_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 15px !important;
    font-weight: 600 !important;
}

.o_gantt_view .o_gantt_row {
    border-bottom: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_gantt_view .o_gantt_row:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

/* Enhanced Map View (.o_map_view) */
.o_map_view {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
}

.o_map_view .o_map_container {
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Enhanced Dashboard View (.o_dashboard) */
.o_dashboard {
    background: transparent !important;
    padding: 20px !important;
}

.o_dashboard .o_dashboard_action {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin-bottom: 20px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.o_dashboard .o_dashboard_action:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
}

.o_dashboard .o_dashboard_action .o_dashboard_action_title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 15px 20px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
}
/* ========================================
   ADVANCED XML VIEWS ENHANCEMENTS
   ======================================== */

/* Enhanced Cohort View (.o_cohort_view) */
.o_cohort_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_cohort_view table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 15px 10px !important;
    border: none !important;
    text-align: center !important;
}

.o_cohort_view table tbody td {
    padding: 12px 8px !important;
    text-align: center !important;
    border: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_cohort_view table tbody td:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: scale(1.05) !important;
}

/* Enhanced Timeline View (.o_timeline_view) */
.o_timeline_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_timeline_view .o_timeline_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    font-weight: 600 !important;
    font-size: 18px !important;
}

.o_timeline_view .vis-item {
    border-radius: 8px !important;
    border: none !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.o_timeline_view .vis-item:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Enhanced Hierarchy View (.o_hierarchy_view) */
.o_hierarchy_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    padding: 20px !important;
}

.o_hierarchy_view .o_hierarchy_node {
    background: rgba(248, 249, 250, 0.8) !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 12px !important;
    padding: 15px !important;
    margin: 10px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.o_hierarchy_view .o_hierarchy_node::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 12px 12px 0 0 !important;
}

.o_hierarchy_view .o_hierarchy_node:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
    border-color: #667eea !important;
}

/* Enhanced Studio View (.o_web_studio_view) */
.o_web_studio_view {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.o_web_studio_view .o_web_studio_sidebar {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
}

.o_web_studio_view .o_web_studio_sidebar .o_web_studio_component {
    border-radius: 8px !important;
    margin: 8px !important;
    padding: 12px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
    transition: all 0.3s ease !important;
}

.o_web_studio_view .o_web_studio_sidebar .o_web_studio_component:hover {
    background: rgba(102, 126, 234, 0.05) !important;
    border-color: #667eea !important;
    transform: translateX(5px) !important;
}

/* Enhanced Spreadsheet View (.o_spreadsheet) */
.o_spreadsheet {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_spreadsheet .o-grid {
    border-radius: 8px !important;
}

.o_spreadsheet .o-grid .o-grid-cell {
    border: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_spreadsheet .o-grid .o-grid-cell:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

.o_spreadsheet .o-grid .o-grid-cell.o-selected {
    border: 2px solid #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Notebook/Tabs (.nav-tabs) */
.nav-tabs {
    border-bottom: 2px solid #e9ecef !important;
    margin-bottom: 20px !important;
}

.nav-tabs .nav-link {
    border: none !important;
    border-radius: 8px 8px 0 0 !important;
    padding: 12px 20px !important;
    margin-right: 5px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    color: #6c757d !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.nav-tabs .nav-link:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
    transform: translateY(-2px) !important;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-bottom: 2px solid transparent !important;
}

.nav-tabs .nav-link.active::after {
    content: '' !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    right: 0 !important;
    height: 2px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Enhanced Statusbar (.o_statusbar_status) */
.o_statusbar_status {
    border-radius: 20px !important;
    margin: 0 4px !important;
    padding: 8px 16px !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    background: rgba(248, 249, 250, 0.8) !important;
    color: #6c757d !important;
}

.o_statusbar_status:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
    transform: translateY(-1px) !important;
}

.o_statusbar_status.o_arrow_button_current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-color: #667eea !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.o_statusbar_status.o_arrow_button_done {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
}

/* Enhanced One2many/Many2many Lists */
.o_field_one2many .o_list_view,
.o_field_many2many .o_list_view {
    border-radius: 8px !important;
    border: 2px solid rgba(102, 126, 234, 0.1) !important;
    overflow: hidden !important;
}

.o_field_one2many .o_list_view .o_list_table thead th,
.o_field_many2many .o_list_view .o_list_table thead th {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%) !important;
    color: white !important;
    font-size: 13px !important;
    padding: 12px 8px !important;
}

/* Enhanced Selection Fields */
.o_field_widget.o_field_selection select,
.o_field_widget.o_field_many2one input {
    background: white !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_selection select:focus,
.o_field_widget.o_field_many2one input:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Enhanced Boolean Fields */
.o_field_widget.o_field_boolean input[type="checkbox"] {
    width: 20px !important;
    height: 20px !important;
    border-radius: 4px !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_boolean input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #667eea !important;
}

/* Enhanced Date/DateTime Fields */
.o_field_widget.o_field_date input,
.o_field_widget.o_field_datetime input {
    background: white !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.o_field_widget.o_field_date input:focus,
.o_field_widget.o_field_datetime input:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Text/HTML Fields */
.o_field_widget.o_field_text textarea,
.o_field_widget.o_field_html .note-editor {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_text textarea:focus,
.o_field_widget.o_field_html .note-editor.note-frame.codeview {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Binary/Image Fields */
.o_field_widget.o_field_binary,
.o_field_widget.o_field_image {
    border-radius: 8px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_binary:hover,
.o_field_widget.o_field_image:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important;
}

/* Enhanced Monetary Fields */
.o_field_widget.o_field_monetary input {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(32, 201, 151, 0.05) 100%) !important;
    border: 2px solid rgba(40, 167, 69, 0.2) !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    font-weight: 600 !important;
    color: #28a745 !important;
}

.o_field_widget.o_field_monetary input:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
}

/* Enhanced Progress Bar Fields */
.o_field_widget.o_field_progressbar .progress {
    height: 12px !important;
    border-radius: 6px !important;
    background: #e9ecef !important;
    overflow: hidden !important;
}

.o_field_widget.o_field_progressbar .progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    transition: all 0.3s ease !important;
}

/* Enhanced Priority/Stars Fields */
.o_field_widget.o_priority .o_priority_star {
    color: #ffc107 !important;
    font-size: 18px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_priority .o_priority_star:hover {
    transform: scale(1.2) !important;
    text-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

/* ========================================
   ADVANCED RESPONSIVE DESIGN ENHANCEMENTS
   ======================================== */

/* Mobile-First Responsive Breakpoints */
/* Extra small devices (phones, less than 576px) */
@media (max-width: 575.98px) {
    .o_main_navbar {
        padding: 8px 15px !important;
        min-height: 50px !important;
    }

    .o_menu_brand {
        font-size: 16px !important;
    }

    .o_menu_sections {
        display: none !important;
    }

    .o_menu_systray .o_nav_entry {
        width: 35px !important;
        height: 35px !important;
        margin: 0 2px !important;
    }

    .o_action_manager > .o_view_controller {
        margin: 5px !important;
        border-radius: 6px !important;
    }

    .o_form_sheet {
        margin: 5px !important;
        padding: 15px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 8px 4px !important;
        font-size: 12px !important;
    }

    .o_kanban_record {
        margin-bottom: 10px !important;
        padding: 10px !important;
    }

    .o_control_panel {
        padding: 10px !important;
    }

    .btn {
        padding: 8px 16px !important;
        font-size: 13px !important;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .o_main_navbar {
        padding: 10px 20px !important;
    }

    .o_action_manager > .o_view_controller {
        margin: 10px !important;
        border-radius: 8px !important;
    }

    .o_form_sheet {
        margin: 10px !important;
        padding: 20px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 10px 6px !important;
        font-size: 13px !important;
    }

    .o_kanban_record {
        margin-bottom: 12px !important;
        padding: 12px !important;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .o_action_manager > .o_view_controller {
        margin: 15px !important;
        border-radius: 10px !important;
    }

    .o_form_sheet {
        margin: 15px !important;
        padding: 25px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 12px 8px !important;
        font-size: 14px !important;
    }

    .o_kanban_record {
        margin-bottom: 14px !important;
        padding: 14px !important;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .o_action_manager > .o_view_controller {
        margin: 18px !important;
        border-radius: 11px !important;
    }

    .o_form_sheet {
        margin: 18px !important;
        padding: 28px !important;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .o_action_manager > .o_view_controller {
        margin: 20px !important;
        border-radius: 12px !important;
    }

    .o_form_sheet {
        margin: 20px !important;
        padding: 30px !important;
    }
}

/* ========================================
   ENHANCED MOBILE NAVIGATION
   ======================================== */

/* Mobile Navigation Toggle */
.o_mobile_menu_toggle {
    display: none !important;
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    color: white !important;
    font-size: 18px !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
}

.o_mobile_menu_toggle:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.1) !important;
}

@media (max-width: 991.98px) {
    .o_mobile_menu_toggle {
        display: block !important;
    }

    .o_menu_sections {
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        background: rgba(102, 126, 234, 0.98) !important;
        backdrop-filter: blur(10px) !important;
        transform: translateY(-100%) !important;
        transition: transform 0.3s ease !important;
        z-index: 1000 !important;
        padding: 20px !important;
        border-radius: 0 0 12px 12px !important;
    }

    .o_menu_sections.mobile-open {
        transform: translateY(0) !important;
    }

    .o_menu_sections .o_nav_entry {
        display: block !important;
        width: 100% !important;
        text-align: center !important;
        padding: 12px 20px !important;
        margin: 5px 0 !important;
        border-radius: 8px !important;
    }
}

/* ========================================
   ENHANCED GRID SYSTEM FOR FORMS
   ======================================== */

/* Responsive Form Grid */
.o_group.o_inner_group {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 15px !important;
}

@media (min-width: 576px) {
    .o_group.o_inner_group {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    }
}

@media (min-width: 992px) {
    .o_group.o_inner_group {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    }
}

/* Responsive Kanban Columns */
.o_kanban_view .o_kanban_group {
    min-width: 250px !important;
}

@media (max-width: 767.98px) {
    .o_kanban_view .o_kanban_group {
        min-width: 200px !important;
        margin: 0 5px !important;
    }

    .o_kanban_view {
        padding: 10px !important;
    }
}

@media (max-width: 575.98px) {
    .o_kanban_view .o_kanban_group {
        min-width: 180px !important;
        margin: 0 2px !important;
    }

    .o_kanban_view {
        padding: 5px !important;
    }
}

/* ========================================
   ENHANCED LIST VIEW RESPONSIVENESS
   ======================================== */

/* Responsive Table */
.o_list_view {
    overflow-x: auto !important;
}

.o_list_table {
    min-width: 600px !important;
    width: 100% !important;
}

@media (max-width: 767.98px) {
    .o_list_table {
        min-width: 500px !important;
    }

    .o_list_table thead th:nth-child(n+4),
    .o_list_table tbody td:nth-child(n+4) {
        display: none !important;
    }
}

@media (max-width: 575.98px) {
    .o_list_table {
        min-width: 400px !important;
    }

    .o_list_table thead th:nth-child(n+3),
    .o_list_table tbody td:nth-child(n+3) {
        display: none !important;
    }
}

/* ========================================
   ENHANCED DARK MODE SUPPORT
   ======================================== */

/* Dark Mode Variables */
:root {
    --lindo-primary: #667eea;
    --lindo-secondary: #764ba2;
    --lindo-background: #ffffff;
    --lindo-surface: #f8f9fa;
    --lindo-text: #000000;
    --lindo-border: #e9ecef;
}

[data-theme="dark"] {
    --lindo-background: #1a1a1a;
    --lindo-surface: #2d2d2d;
    --lindo-text: #111010;
    --lindo-border: #404040;
}

/* Dark Mode Styles */
@media (prefers-color-scheme: dark) {
    .o_web_client {
        background: var(--lindo-background) !important;
        color: var(--lindo-text) !important;
    }

    .o_action_manager > .o_view_controller,
    .o_form_sheet,
    .o_kanban_record,
    .o_list_view,
    .o_calendar_view,
    .o_pivot_view,
    .o_graph_view {
        background: var(--lindo-surface) !important;
        color: var(--lindo-text) !important;
        border-color: var(--lindo-border) !important;
    }

    .o_field_widget input,
    .o_field_widget select,
    .o_field_widget textarea {
        background: var(--lindo-surface) !important;
        color: var(--lindo-text) !important;
        border-color: var(--lindo-border) !important;
    }
}

/* ========================================
   ENHANCED ACCESSIBILITY FEATURES
   ======================================== */

/* Focus Indicators */
.o_field_widget input:focus,
.o_field_widget select:focus,
.o_field_widget textarea:focus,
.btn:focus,
.o_nav_entry:focus,
.dropdown-toggle:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5) !important;
    outline-offset: 2px !important;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .o_main_navbar {
        background: #000000 !important;
        border-bottom: 2px solid #ffffff !important;
    }

    .o_list_table thead th {
        background: #000000 !important;
        color: #ffffff !important;
        border: 1px solid #ffffff !important;
    }

    .btn-primary {
        background: #0000ff !important;
        border: 2px solid #ffffff !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ========================================
   ENHANCED PRINT STYLES
   ======================================== */

@media print {
    .o_main_navbar,
    .o_control_panel,
    .btn,
    .o_statusbar_status {
        display: none !important;
    }

    .o_action_manager > .o_view_controller {
        margin: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
    }

    .o_form_sheet {
        margin: 0 !important;
        padding: 20px !important;
        box-shadow: none !important;
    }

    .o_list_table,
    .o_kanban_record {
        box-shadow: none !important;
        border: 1px solid #000000 !important;
    }

    .o_list_table thead th {
        background: #f0f0f0 !important;
        color: #000000 !important;
    }

    * {
        color: #000000 !important;
        background: transparent !important;
    }
}

/* ========================================
   ENHANCED ANIMATIONS AND TRANSITIONS
   ======================================== */

/* Staggered Animations for Lists */
.o_list_view .o_list_table tbody tr {
    animation: slideInFromLeft 0.6s ease-out forwards !important;
    opacity: 0 !important;
    transform: translateX(-20px) !important;
}

.o_list_view .o_list_table tbody tr:nth-child(1) { animation-delay: 0.1s !important; }
.o_list_view .o_list_table tbody tr:nth-child(2) { animation-delay: 0.2s !important; }
.o_list_view .o_list_table tbody tr:nth-child(3) { animation-delay: 0.3s !important; }
.o_list_view .o_list_table tbody tr:nth-child(4) { animation-delay: 0.4s !important; }
.o_list_view .o_list_table tbody tr:nth-child(5) { animation-delay: 0.5s !important; }
.o_list_view .o_list_table tbody tr:nth-child(n+6) { animation-delay: 0.6s !important; }

@keyframes slideInFromLeft {
    to {
        opacity: 1 !important;
        transform: translateX(0) !important;
    }
}

/* Staggered Animations for Kanban Cards */
.o_kanban_view .o_kanban_record {
    animation: fadeInScale 0.6s ease-out forwards !important;
    opacity: 0 !important;
    transform: scale(0.9) !important;
}

.o_kanban_view .o_kanban_record:nth-child(1) { animation-delay: 0.1s !important; }
.o_kanban_view .o_kanban_record:nth-child(2) { animation-delay: 0.2s !important; }
.o_kanban_view .o_kanban_record:nth-child(3) { animation-delay: 0.3s !important; }
.o_kanban_view .o_kanban_record:nth-child(4) { animation-delay: 0.4s !important; }
.o_kanban_view .o_kanban_record:nth-child(5) { animation-delay: 0.5s !important; }
.o_kanban_view .o_kanban_record:nth-child(n+6) { animation-delay: 0.6s !important; }

@keyframes fadeInScale {
    to {
        opacity: 1 !important;
        transform: scale(1) !important;
    }
}

/* Enhanced Loading States */
.o_view_controller.o_view_controller_loading {
    position: relative !important;
}

.o_view_controller.o_view_controller_loading::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(2px) !important;
    z-index: 1000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.o_view_controller.o_view_controller_loading::after {
    content: '' !important;
    width: 40px !important;
    height: 40px !important;
    border: 4px solid #f3f3f3 !important;
    border-top: 4px solid #667eea !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
    z-index: 1001 !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}