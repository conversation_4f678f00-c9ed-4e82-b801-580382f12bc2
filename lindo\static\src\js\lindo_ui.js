/**
 * Lindo Enhanced UI JavaScript
 * Simple enhancements for Odoo UI components
 */

// Simple vanilla JavaScript approach for better compatibility
(function() {
    'use strict';

    // Wait for DOM to be ready
    function ready(fn) {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    }

    // Enhanced navbar scroll effect
    function enhanceNavbar() {
        var navbar = document.querySelector('.o_main_navbar');
        if (!navbar) return;

        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Enhance dropdown animations
        var dropdowns = navbar.querySelectorAll('.dropdown');
        dropdowns.forEach(function(dropdown) {
            dropdown.addEventListener('show.bs.dropdown', function() {
                var menu = this.querySelector('.dropdown-menu');
                if (menu) {
                    menu.classList.add('fade-in');
                }
            });
        });
    }

    // // Enhanced list views
    // function enhanceListViews() {
    //     var listViews = document.querySelectorAll('.o_list_view');
    //     listViews.forEach(function(listView) {
    //         listView.classList.add('fade-in');
            
    //         var rows = listView.querySelectorAll('tbody tr');
    //         rows.forEach(function(row) {
    //             row.addEventListener('mouseenter', function() {
    //                 this.classList.add('slide-in-left');
    //             });
    //             row.addEventListener('mouseleave', function() {
    //                 this.classList.remove('slide-in-left');
    //             });
    //         });
    //     });
    // }

    // // Enhanced form views
    // function enhanceFormViews() {
    //     var formViews = document.querySelectorAll('.o_form_view');
    //     formViews.forEach(function(formView) {
    //         formView.classList.add('fade-in');
            
    //         var fields = formView.querySelectorAll('.o_field_widget input, .o_field_widget select, .o_field_widget textarea');
    //         fields.forEach(function(field) {
    //             field.addEventListener('focus', function() {
    //                 var widget = this.closest('.o_field_widget');
    //                 if (widget) {
    //                     widget.classList.add('focused');
    //                 }
    //             });
    //             field.addEventListener('blur', function() {
    //                 var widget = this.closest('.o_field_widget');
    //                 if (widget) {
    //                     widget.classList.remove('focused');
    //                 }
    //             });
    //         });
    //     });
    // }

    // // Enhanced kanban views
    // function enhanceKanbanViews() {
    //     var kanbanViews = document.querySelectorAll('.o_kanban_view');
    //     kanbanViews.forEach(function(kanbanView) {
    //         var records = kanbanView.querySelectorAll('.o_kanban_record');
    //         records.forEach(function(record, index) {
    //             setTimeout(function() {
    //                 record.classList.add('fade-in');
    //             }, index * 100);
    //         });
    //     });
    // }

    // // Enhanced calendar views
    // function enhanceCalendarViews() {
    //     var calendarViews = document.querySelectorAll('.o_calendar_view');
    //     calendarViews.forEach(function(calendarView) {
    //         calendarView.classList.add('fade-in');
            
    //         // Enhance calendar events
    //         var events = calendarView.querySelectorAll('.fc-event');
    //         events.forEach(function(event) {
    //             event.addEventListener('mouseenter', function() {
    //                 this.style.transform = 'scale(1.05)';
    //             });
    //             event.addEventListener('mouseleave', function() {
    //                 this.style.transform = 'scale(1)';
    //             });
    //         });
    //     });
    // }

    // // Enhanced pivot views
    // function enhancePivotViews() {
    //     var pivotViews = document.querySelectorAll('.o_pivot_view');
    //     pivotViews.forEach(function(pivotView) {
    //         pivotView.classList.add('fade-in');
            
    //         var cells = pivotView.querySelectorAll('tbody td');
    //         cells.forEach(function(cell) {
    //             cell.addEventListener('mouseenter', function() {
    //                 this.style.background = 'rgba(102, 126, 234, 0.1)';
    //             });
    //             cell.addEventListener('mouseleave', function() {
    //                 this.style.background = '';
    //             });
    //         });
    //     });
    // }

    // // Enhanced graph views
    // function enhanceGraphViews() {
    //     var graphViews = document.querySelectorAll('.o_graph_view');
    //     graphViews.forEach(function(graphView) {
    //         graphView.classList.add('fade-in');
    //     });
    // }

    // // Enhanced search views
    // function enhanceSearchViews() {
    //     var searchViews = document.querySelectorAll('.o_searchview');
    //     searchViews.forEach(function(searchView) {
    //         var input = searchView.querySelector('.o_searchview_input');
    //         if (input) {
    //             input.addEventListener('focus', function() {
    //                 searchView.classList.add('focused');
    //             });
    //             input.addEventListener('blur', function() {
    //                 searchView.classList.remove('focused');
    //             });
    //         }
    //     });
    // }

    // // Enhanced activity views
    // function enhanceActivityViews() {
    //     var activityViews = document.querySelectorAll('.o_activity_view');
    //     activityViews.forEach(function(activityView) {
    //         activityView.classList.add('fade-in');
            
    //         var records = activityView.querySelectorAll('.o_activity_record');
    //         records.forEach(function(record) {
    //             record.addEventListener('mouseenter', function() {
    //                 this.style.transform = 'translateX(5px)';
    //             });
    //             record.addEventListener('mouseleave', function() {
    //                 this.style.transform = 'translateX(0)';
    //             });
    //         });
    //     });
    // }

    // // Enhanced gantt views
    // function enhanceGanttViews() {
    //     var ganttViews = document.querySelectorAll('.o_gantt_view');
    //     ganttViews.forEach(function(ganttView) {
    //         ganttView.classList.add('fade-in');
            
    //         var rows = ganttView.querySelectorAll('.o_gantt_row');
    //         rows.forEach(function(row) {
    //             row.addEventListener('mouseenter', function() {
    //                 this.style.background = 'rgba(102, 126, 234, 0.05)';
    //             });
    //             row.addEventListener('mouseleave', function() {
    //                 this.style.background = '';
    //             });
    //         });
    //     });
    // }

    // // Enhanced dashboard views
    // function enhanceDashboardViews() {
    //     var dashboards = document.querySelectorAll('.o_dashboard');
    //     dashboards.forEach(function(dashboard) {
    //         var actions = dashboard.querySelectorAll('.o_dashboard_action');
    //         actions.forEach(function(action, index) {
    //             setTimeout(function() {
    //                 action.classList.add('fade-in');
    //             }, index * 150);
                
    //             action.addEventListener('mouseenter', function() {
    //                 this.style.transform = 'translateY(-3px)';
    //             });
    //             action.addEventListener('mouseleave', function() {
    //                 this.style.transform = 'translateY(0)';
    //             });
    //         });
    //     });
    // }

    // // Enhanced tabs/notebook
    // function enhanceTabs() {
    //     var tabs = document.querySelectorAll('.nav-tabs .nav-link');
    //     tabs.forEach(function(tab) {
    //         tab.addEventListener('mouseenter', function() {
    //             if (!this.classList.contains('active')) {
    //                 this.style.transform = 'translateY(-2px)';
    //             }
    //         });
    //         tab.addEventListener('mouseleave', function() {
    //             if (!this.classList.contains('active')) {
    //                 this.style.transform = 'translateY(0)';
    //             }
    //         });
    //     });
    // }

    // // Enhanced statusbar
    // function enhanceStatusbar() {
    //     var statusItems = document.querySelectorAll('.o_statusbar_status');
    //     statusItems.forEach(function(item) {
    //         item.addEventListener('mouseenter', function() {
    //             if (!this.classList.contains('o_arrow_button_current')) {
    //                 this.style.transform = 'translateY(-1px)';
    //             }
    //         });
    //         item.addEventListener('mouseleave', function() {
    //             if (!this.classList.contains('o_arrow_button_current')) {
    //                 this.style.transform = 'translateY(0)';
    //             }
    //         });
    //     });
    // }

    // // Enhanced field widgets
    // function enhanceFieldWidgets() {
    //     // Enhance all input fields
    //     var inputs = document.querySelectorAll('.o_field_widget input, .o_field_widget select, .o_field_widget textarea');
    //     inputs.forEach(function(input) {
    //         input.addEventListener('focus', function() {
    //             var widget = this.closest('.o_field_widget');
    //             if (widget) {
    //                 widget.classList.add('focused');
    //             }
    //         });
    //         input.addEventListener('blur', function() {
    //             var widget = this.closest('.o_field_widget');
    //             if (widget) {
    //                 widget.classList.remove('focused');
    //             }
    //         });
    //     });

    //     // Enhance priority stars
    //     var stars = document.querySelectorAll('.o_priority .o_priority_star');
    //     stars.forEach(function(star) {
    //         star.addEventListener('mouseenter', function() {
    //             this.style.transform = 'scale(1.2)';
    //         });
    //         star.addEventListener('mouseleave', function() {
    //             this.style.transform = 'scale(1)';
    //         });
    //     });

    //     // Enhance binary/image fields
    //     var binaryFields = document.querySelectorAll('.o_field_binary, .o_field_image');
    //     binaryFields.forEach(function(field) {
    //         field.addEventListener('mouseenter', function() {
    //             this.style.transform = 'scale(1.02)';
    //         });
    //         field.addEventListener('mouseleave', function() {
    //             this.style.transform = 'scale(1)';
    //         });
    //     });
    // }

    // // Enhanced buttons
    // function enhanceButtons() {
    //     document.addEventListener('click', function(e) {
    //         if (e.target.classList.contains('btn')) {
    //             e.target.classList.add('clicked');
    //             setTimeout(function() {
    //                 e.target.classList.remove('clicked');
    //             }, 200);
    //         }
    //     });
    // }

    // // Enhanced modals
    // function enhanceModals() {
    //     document.addEventListener('show.bs.modal', function(e) {
    //         var modal = e.target.querySelector('.modal-content');
    //         if (modal) {
    //             modal.classList.add('fade-in');
    //         }
    //     });
    // }

    // // Smooth scrolling for anchor links
    // function setupSmoothScrolling() {
    //     document.addEventListener('click', function(e) {
    //         var target = e.target;
    //         if (target.tagName === 'A' && target.getAttribute('href') && target.getAttribute('href').startsWith('#')) {
    //             var targetElement = document.querySelector(target.getAttribute('href'));
    //             if (targetElement) {
    //                 e.preventDefault();
    //                 targetElement.scrollIntoView({
    //                     behavior: 'smooth',
    //                     block: 'start'
    //                 });
    //             }
    //         }
    //     });
    // }

    // // Intersection Observer for fade-in animations
    // function setupIntersectionObserver() {
    //     if ('IntersectionObserver' in window) {
    //         var observer = new IntersectionObserver(function(entries) {
    //             entries.forEach(function(entry) {
    //                 if (entry.isIntersecting) {
    //                     entry.target.classList.add('fade-in');
    //                 }
    //             });
    //         }, {
    //             threshold: 0.1,
    //             rootMargin: '0px 0px -50px 0px'
    //         });

    //         // Observe view controllers
    //         var viewControllers = document.querySelectorAll('.o_view_controller');
    //         viewControllers.forEach(function(controller) {
    //             observer.observe(controller);
    //         });
    //     }
    // }

    // // Re-enhance views when new content is loaded
    // function setupMutationObserver() {
    //     if ('MutationObserver' in window) {
    //         var observer = new MutationObserver(function(mutations) {
    //             mutations.forEach(function(mutation) {
    //                 mutation.addedNodes.forEach(function(node) {
    //                     if (node.nodeType === 1) { // Element node
    //                         if (node.classList && node.classList.contains('o_view_controller')) {
    //                             setTimeout(function() {
    //                                 enhanceListViews();
    //                                 enhanceFormViews();
    //                                 enhanceKanbanViews();
    //                             }, 100);
    //                         }
    //                     }
    //                 });
    //             });
    //         });

    //         observer.observe(document.body, {
    //             childList: true,
    //             subtree: true
    //         });
    //     }
    // }

    // Initialize all enhancements
    function initializeEnhancements() {
        enhanceNavbar();
        // enhanceListViews();
        // enhanceFormViews();
        // enhanceKanbanViews();
        // enhanceButtons();
        // enhanceModals();
        // setupSmoothScrolling();
        // setupIntersectionObserver();
        // setupMutationObserver();
    }

    // Start when DOM is ready
    // ready(initializeEnhancements);

    // // Re-initialize when page changes (for SPA behavior)
    // window.addEventListener('popstate', function() {
    //     setTimeout(initializeEnhancements, 100);
    // });

    // // Enhanced Mobile Navigation
    // function enhanceMobileNavigation() {
    //     // Create mobile menu toggle button
    //     var navbar = document.querySelector('.o_main_navbar');
    //     if (!navbar) return;
        
    //     var menuSections = navbar.querySelector('.o_menu_sections');
    //     if (!menuSections) return;
        
    //     // Check if toggle button already exists
    //     var existingToggle = navbar.querySelector('.o_mobile_menu_toggle');
    //     if (existingToggle) return;
        
    //     var toggleButton = document.createElement('button');
    //     toggleButton.className = 'o_mobile_menu_toggle';
    //     toggleButton.innerHTML = '☰';
    //     toggleButton.setAttribute('aria-label', 'Toggle mobile menu');
        
    //     // Insert toggle button
    //     navbar.appendChild(toggleButton);
        
    //     // Toggle menu functionality
    //     toggleButton.addEventListener('click', function() {
    //         menuSections.classList.toggle('mobile-open');
    //         toggleButton.innerHTML = menuSections.classList.contains('mobile-open') ? '✕' : '☰';
    //     });
        
    //     // Close menu when clicking outside
    //     document.addEventListener('click', function(e) {
    //         if (!navbar.contains(e.target) && menuSections.classList.contains('mobile-open')) {
    //             menuSections.classList.remove('mobile-open');
    //             toggleButton.innerHTML = '☰';
    //         }
    //     });
        
    //     // Close menu on window resize if desktop
    //     window.addEventListener('resize', function() {
    //         if (window.innerWidth > 991) {
    //             menuSections.classList.remove('mobile-open');
    //             toggleButton.innerHTML = '☰';
    //         }
    //     });
    // }

    // // Enhanced Dark Mode Toggle
    // function enhanceDarkMode() {
    //     // Create dark mode toggle
    //     var navbar = document.querySelector('.o_main_navbar');
    //     if (!navbar) return;
        
    //     var systray = navbar.querySelector('.o_menu_systray');
    //     if (!systray) return;
        
    //     var darkModeToggle = document.createElement('li');
    //     darkModeToggle.className = 'o_nav_entry';
    //     darkModeToggle.innerHTML = '<a href="#" class="o_nav_entry" id="dark-mode-toggle" title="Toggle Dark Mode">🌙</a>';
        
    //     systray.appendChild(darkModeToggle);
        
    //     var toggleLink = darkModeToggle.querySelector('#dark-mode-toggle');
        
    //     // Check saved preference
    //     var isDarkMode = localStorage.getItem('lindo-dark-mode') === 'true';
    //     if (isDarkMode) {
    //         document.documentElement.setAttribute('data-theme', 'dark');
    //         toggleLink.innerHTML = '☀️';
    //     }
        
    //     toggleLink.addEventListener('click', function(e) {
    //         e.preventDefault();
    //         var currentTheme = document.documentElement.getAttribute('data-theme');
    //         var newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
    //         document.documentElement.setAttribute('data-theme', newTheme);
    //         localStorage.setItem('lindo-dark-mode', newTheme === 'dark');
            
    //         toggleLink.innerHTML = newTheme === 'dark' ? '☀️' : '🌙';
    //         toggleLink.title = newTheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode';
    //     });
    // }

    // // Enhanced Touch Gestures for Mobile
    // function enhanceTouchGestures() {
    //     if (!('ontouchstart' in window)) return;
        
    //     var startX, startY, currentX, currentY;
        
    //     // Swipe to navigate in kanban
    //     var kanbanViews = document.querySelectorAll('.o_kanban_view');
    //     kanbanViews.forEach(function(kanbanView) {
    //         kanbanView.addEventListener('touchstart', function(e) {
    //             startX = e.touches[0].clientX;
    //             startY = e.touches[0].clientY;
    //         });
            
    //         kanbanView.addEventListener('touchmove', function(e) {
    //             if (!startX || !startY) return;
                
    //             currentX = e.touches[0].clientX;
    //             currentY = e.touches[0].clientY;
                
    //             var diffX = startX - currentX;
    //             var diffY = startY - currentY;
                
    //             if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
    //                 // Horizontal swipe detected
    //                 if (diffX > 0) {
    //                     // Swipe left - scroll right
    //                     kanbanView.scrollLeft += 100;
    //                 } else {
    //                     // Swipe right - scroll left
    //                     kanbanView.scrollLeft -= 100;
    //                 }
    //             }
                
    //             startX = null;
    //             startY = null;
    //         });
    //     });
    // }

    // // Enhanced Keyboard Shortcuts
    // function enhanceKeyboardShortcuts() {
    //     document.addEventListener('keydown', function(e) {
    //         // Ctrl/Cmd + Shift + D for dark mode
    //         if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
    //             e.preventDefault();
    //             var darkModeToggle = document.querySelector('#dark-mode-toggle');
    //             if (darkModeToggle) {
    //                 darkModeToggle.click();
    //             }
    //         }
            
    //         // Ctrl/Cmd + Shift + M for mobile menu
    //         if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'M') {
    //             e.preventDefault();
    //             var mobileToggle = document.querySelector('.o_mobile_menu_toggle');
    //             if (mobileToggle && window.innerWidth <= 991) {
    //                 mobileToggle.click();
    //             }
    //         }
            
    //         // ESC to close modals and menus
    //         if (e.key === 'Escape') {
    //             var openMenu = document.querySelector('.o_menu_sections.mobile-open');
    //             if (openMenu) {
    //                 var toggle = document.querySelector('.o_mobile_menu_toggle');
    //                 if (toggle) toggle.click();
    //             }
    //         }
    //     });
    // }

    // // Enhanced Loading States
    // function enhanceLoadingStates() {
    //     // Intercept view changes
    //     var actionManager = document.querySelector('.o_action_manager');
    //     if (!actionManager) return;
        
    //     var observer = new MutationObserver(function(mutations) {
    //         mutations.forEach(function(mutation) {
    //             mutation.addedNodes.forEach(function(node) {
    //                 if (node.nodeType === 1 && node.classList && node.classList.contains('o_view_controller')) {
    //                     // Add loading state
    //                     node.classList.add('o_view_controller_loading');
                        
    //                     // Remove loading state after content loads
    //                     setTimeout(function() {
    //                         node.classList.remove('o_view_controller_loading');
    //                     }, 500);
    //                 }
    //             });
    //         });
    //     });
        
    //     observer.observe(actionManager, {
    //         childList: true,
    //         subtree: true
    //     });
    // }

    // // Enhanced Context Menus
    // function enhanceContextMenus() {
    //     // Add right-click context menu for records
    //     document.addEventListener('contextmenu', function(e) {
    //         var record = e.target.closest('.o_list_table tbody tr, .o_kanban_record');
    //         if (!record) return;
            
    //         e.preventDefault();
            
    //         // Create context menu
    //         var existingMenu = document.querySelector('.lindo-context-menu');
    //         if (existingMenu) {
    //             existingMenu.remove();
    //         }
            
    //         var contextMenu = document.createElement('div');
    //         contextMenu.className = 'lindo-context-menu';
    //         contextMenu.style.cssText = `
    //             position: fixed;
    //             top: ${e.clientY}px;
    //             left: ${e.clientX}px;
    //             background: white;
    //             border: 1px solid #ddd;
    //             border-radius: 6px;
    //             box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    //             z-index: 10000;
    //             min-width: 150px;
    //             overflow: hidden;
    //         `;
            
    //         var menuItems = [
    //             { text: 'Open', icon: '📂' },
    //             { text: 'Edit', icon: '✏️' },
    //             { text: 'Duplicate', icon: '📋' },
    //             { text: 'Delete', icon: '🗑️' }
    //         ];
            
    //         menuItems.forEach(function(item) {
    //             var menuItem = document.createElement('div');
    //             menuItem.className = 'lindo-context-menu-item';
    //             menuItem.style.cssText = `
    //                 padding: 8px 12px;
    //                 cursor: pointer;
    //                 border-bottom: 1px solid #f0f0f0;
    //                 display: flex;
    //                 align-items: center;
    //                 gap: 8px;
    //                 transition: background-color 0.2s;
    //             `;
    //             menuItem.innerHTML = `<span>${item.icon}</span> ${item.text}`;
                
    //             menuItem.addEventListener('mouseenter', function() {
    //                 this.style.backgroundColor = '#f8f9fa';
    //             });
    //             menuItem.addEventListener('mouseleave', function() {
    //                 this.style.backgroundColor = 'transparent';
    //             });
                
    //             contextMenu.appendChild(menuItem);
    //         });
            
    //         document.body.appendChild(contextMenu);
            
    //         // Close context menu when clicking elsewhere
    //         setTimeout(function() {
    //             document.addEventListener('click', function closeContextMenu() {
    //                 contextMenu.remove();
    //                 document.removeEventListener('click', closeContextMenu);
    //             });
    //         }, 10);
    //     });
    // }

    // // Enhanced Drag and Drop
    // function enhanceDragAndDrop() {
    //     // Make kanban cards draggable
    //     var kanbanRecords = document.querySelectorAll('.o_kanban_record');
    //     kanbanRecords.forEach(function(record) {
    //         record.setAttribute('draggable', 'true');
            
    //         record.addEventListener('dragstart', function(e) {
    //             e.dataTransfer.setData('text/plain', record.id || 'kanban-record');
    //             record.style.opacity = '0.5';
    //         });
            
    //         record.addEventListener('dragend', function(e) {
    //             record.style.opacity = '1';
    //         });
    //     });
        
    //     // Make kanban columns drop zones
    //     var kanbanColumns = document.querySelectorAll('.o_kanban_group');
    //     kanbanColumns.forEach(function(column) {
    //         column.addEventListener('dragover', function(e) {
    //             e.preventDefault();
    //             column.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
    //         });
            
    //         column.addEventListener('dragleave', function(e) {
    //             column.style.backgroundColor = '';
    //         });
            
    //         column.addEventListener('drop', function(e) {
    //             e.preventDefault();
    //             column.style.backgroundColor = '';
    //             // Add visual feedback for drop
    //             var dropEffect = document.createElement('div');
    //             dropEffect.style.cssText = `
    //                 position: absolute;
    //                 top: 50%;
    //                 left: 50%;
    //                 transform: translate(-50%, -50%);
    //                 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    //                 color: white;
    //                 padding: 8px 16px;
    //                 border-radius: 20px;
    //                 font-size: 12px;
    //                 pointer-events: none;
    //                 z-index: 1000;
    //             `;
    //             dropEffect.textContent = 'Moved!';
    //             column.style.position = 'relative';
    //             column.appendChild(dropEffect);
                
    //             setTimeout(function() {
    //                 dropEffect.remove();
    //             }, 1000);
    //         });
    //     });
    // }

    // // Enhanced Performance Monitoring
    // function enhancePerformanceMonitoring() {
    //     // Monitor page load performance
    //     window.addEventListener('load', function() {
    //         setTimeout(function() {
    //             if (window.performance && window.performance.timing) {
    //                 var timing = window.performance.timing;
    //                 var loadTime = timing.loadEventEnd - timing.navigationStart;
                    
    //                 if (loadTime > 3000) {
    //                     console.warn('Lindo UI: Slow page load detected (' + loadTime + 'ms)');
    //                 }
    //             }
    //         }, 100);
    //     });
        
    //     // Monitor view switching performance
    //     var viewSwitchStart;
    //     document.addEventListener('click', function(e) {
    //         if (e.target.closest('.o_cp_switch_buttons button')) {
    //             viewSwitchStart = performance.now();
    //         }
    //     });
        
    //     var observer = new MutationObserver(function(mutations) {
    //         if (viewSwitchStart) {
    //             var switchTime = performance.now() - viewSwitchStart;
    //             if (switchTime > 1000) {
    //                 console.warn('Lindo UI: Slow view switch detected (' + switchTime.toFixed(2) + 'ms)');
    //             }
    //             viewSwitchStart = null;
    //         }
    //     });
        
    //     var actionManager = document.querySelector('.o_action_manager');
    //     if (actionManager) {
    //         observer.observe(actionManager, {
    //             childList: true,
    //             subtree: true
    //         });
    //     }
    // }

    // Update initialization function
    function initializeEnhancements() {
        enhanceNavbar();
        // enhanceListViews();
        // enhanceFormViews();
        // enhanceKanbanViews();
        // enhanceCalendarViews();
        // enhancePivotViews();
        // enhanceGraphViews();
        // enhanceSearchViews();
        // enhanceActivityViews();
        // enhanceGanttViews();
        // enhanceDashboardViews();
        // enhanceTabs();
        // enhanceStatusbar();
        // enhanceFieldWidgets();
        // enhanceButtons();
        // enhanceModals();
        // enhanceMobileNavigation();
        // enhanceDarkMode();
        // enhanceTouchGestures();
        // enhanceKeyboardShortcuts();
        // enhanceLoadingStates();
        // enhanceContextMenus();
        // enhanceDragAndDrop();
        // enhancePerformanceMonitoring();
        // setupSmoothScrolling();
        // setupIntersectionObserver();
        // setupMutationObserver();
    }

})();

// // Odoo module definition (enhanced for better integration)
// if (typeof odoo !== 'undefined') {
//     odoo.define('lindo.enhanced_ui', ['web.core', 'web.Widget'], function(require) {
//         'use strict';
        
//         var core = require('web.core');
//         var Widget = require('web.Widget');
        
//         var LindoUI = Widget.extend({
//             template: 'lindo.ui_template',
            
//             init: function() {
//                 this._super.apply(this, arguments);
//                 this.setupEnhancements();
//             },
            
//             setupEnhancements: function() {
//                 // The enhancements are already applied via vanilla JS above
//                 // This provides Odoo-specific integration
                
//                 // Listen to Odoo events
//                 core.bus.on('view_content_has_changed', this, this.onViewChange);
//                 core.bus.on('kanban_record_update', this, this.onKanbanUpdate);
//             },
            
//             onViewChange: function() {
//                 // Re-apply enhancements when view changes
//                 setTimeout(function() {
//                     // Re-initialize specific enhancements
//                     var event = new CustomEvent('lindoViewChanged');
//                     document.dispatchEvent(event);
//                 }, 100);
//             },
            
//             onKanbanUpdate: function() {
//                 // Re-apply kanban enhancements
//                 setTimeout(function() {
//                     var kanbanViews = document.querySelectorAll('.o_kanban_view');
//                     kanbanViews.forEach(function(view) {
//                         view.classList.add('fade-in');
//                     });
//                 }, 50);
//             }
//         });
        
//         return {
//             LindoUI: LindoUI,
//             name: 'Lindo Enhanced UI',
//             version: '2.0.0',
//             features: [
//                 'Responsive Design',
//                 'Dark Mode',
//                 'Enhanced Animations',
//                 'Mobile Optimizations',
//                 'Accessibility Features',
//                 'Performance Monitoring'
//             ]
//         };
//     });
// }


/** @odoo-module **/

// odoo.define('lindo.enhanced_ui', ['web.core', 'web.Widget'], function (require) {
//     'use strict';

//     const core = require('web.core');
//     const Widget = require('web.Widget');

//     const LindoUI = Widget.extend({
//         template: 'lindo.ui_template',

//         init: function () {
//             this._super.apply(this, arguments);
//         },

//         start: function () {
//             this._super.apply(this, arguments);
//             this.setupEnhancements();
//         },

//         setupEnhancements: function () {
//             core.bus.on('view_content_has_changed', this, this.onViewChange);
//             core.bus.on('kanban_record_update', this, this.onKanbanUpdate);
//         },

//         onViewChange: function () {
//             setTimeout(() => {
//                 const event = new CustomEvent('lindoViewChanged');
//                 document.dispatchEvent(event);
//             }, 100);
//         },

//         onKanbanUpdate: function () {
//             setTimeout(() => {
//                 document.querySelectorAll('.o_kanban_view').forEach((view) => {
//                     view.classList.add('fade-in');
//                 });
//             }, 50);
//         },
//     });

//     core.bus.on('web_client_ready', null, function () {
//         const widget = new LindoUI();
//         widget.appendTo(document.body);
//     });

//     return LindoUI;
// });
