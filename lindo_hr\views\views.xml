<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="lindo_hr.list">
      <field name="name">lindo_hr list</field>
      <field name="model">lindo_hr.lindo_hr</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="lindo_hr.action_window">
      <field name="name">lindo_hr window</field>
      <field name="res_model">lindo_hr.lindo_hr</field>
      <field name="view_mode">tree,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="lindo_hr.action_server">
      <field name="name">lindo_hr server</field>
      <field name="model_id" ref="model_lindo_hr_lindo_hr"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="lindo_hr" id="lindo_hr.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="lindo_hr.menu_1" parent="lindo_hr.menu_root"/>
    <menuitem name="Menu 2" id="lindo_hr.menu_2" parent="lindo_hr.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="lindo_hr.menu_1_list" parent="lindo_hr.menu_1"
              action="lindo_hr.action_window"/>
    <menuitem name="Server to list" id="lindo_hr" parent="lindo_hr.menu_2"
              action="lindo_hr.action_server"/>
-->
  </data>
</odoo>
