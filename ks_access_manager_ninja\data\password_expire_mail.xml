<odoo>
    <data>
        <record id="email_template_password_expiration_before_seven" model="mail.template">
            <field name="name">Password Expiration Reminder Before Seven Days </field>
            <field name="model_id" ref="base.model_res_users"/>
            <field name="subject">Urgent: Password Expiration Notice</field>
            <field name="email_from">{{ object.env.user.email }}</field>
            <field name="email_to">{{ object.email }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    Dear <span style="font-weight: bold;" t-out="object.name"/>,
                    <br/>
                    <br/>

                    Your odoo password is going to expire after 7 days.
                    <br/>
                    <br/>

                    Kindly reset it before <span style="font-weight: bold;" t-out="object.ks_password_expire_date"/> .
                    <div style="margin: 16px 0px 16px 0px;">
                        <a href="/web/reset_password/direct"
                           style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;">
                            Change password
                        </a>
                    </div>
                </div>
            </field>
        </record>

        <record id="email_template_password_expiration_before_one" model="mail.template">
            <field name="name">Password Expiration Reminder before one day </field>
            <field name="model_id" ref="base.model_res_users"/>
            <field name="subject">Urgent: Password Expiration Notice</field>
            <field name="email_from">{{ object.env.user.email }}</field>
            <field name="email_to">{{ object.email }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    Dear <span style="font-weight: bold;" t-out="object.name"/>,
                    <br/>
                    <br/>

                    Your odoo password is going to expire after 1 day.
                    <br/>
                    <br/>

                    Kindly reset it before <span style="font-weight: bold;" t-out="object.ks_password_expire_date"/> .
                    <div style="margin: 16px 0px 16px 0px;">
                        <a href="/web/reset_password/direct"
                           style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;">
                            Change password
                        </a>
                    </div>
                </div>
            </field>
        </record>
    </data>
</odoo>